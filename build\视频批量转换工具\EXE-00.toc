('C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\dist\\视频批量转换工具.exe',
 False,
 False,
 False,
 'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\site-packages\\PyInstaller\\bootloader\\images\\icon-windowed.ico',
 None,
 False,
 False,
 b'<?xml version="1.0" encoding="UTF-8" standalone="yes"?>\n<assembly xmlns='
 b'"urn:schemas-microsoft-com:asm.v1" manifestVersion="1.0">\n  <trustInfo x'
 b'mlns="urn:schemas-microsoft-com:asm.v3">\n    <security>\n      <requested'
 b'Privileges>\n        <requestedExecutionLevel level="asInvoker" uiAccess='
 b'"false"/>\n      </requestedPrivileges>\n    </security>\n  </trustInfo>\n  '
 b'<compatibility xmlns="urn:schemas-microsoft-com:compatibility.v1">\n    <'
 b'application>\n      <supportedOS Id="{e2011457-1546-43c5-a5fe-008deee3d3f'
 b'0}"/>\n      <supportedOS Id="{35138b9a-5d96-4fbd-8e2d-a2440225f93a}"/>\n '
 b'     <supportedOS Id="{4a2f28e3-53b9-4441-ba9c-d69d4a4a6e38}"/>\n      <s'
 b'upportedOS Id="{1f676c76-80e1-4239-95bb-83d0f6d0da78}"/>\n      <supporte'
 b'dOS Id="{8e0f7a12-bfb3-4fe8-b9a5-48fd50a15a9a}"/>\n    </application>\n  <'
 b'/compatibility>\n  <application xmlns="urn:schemas-microsoft-com:asm.v3">'
 b'\n    <windowsSettings>\n      <longPathAware xmlns="http://schemas.micros'
 b'oft.com/SMI/2016/WindowsSettings">true</longPathAware>\n    </windowsSett'
 b'ings>\n  </application>\n  <dependency>\n    <dependentAssembly>\n      <ass'
 b'emblyIdentity type="win32" name="Microsoft.Windows.Common-Controls" version='
 b'"6.0.0.0" processorArchitecture="*" publicKeyToken="6595b64144ccf1df" langua'
 b'ge="*"/>\n    </dependentAssembly>\n  </dependency>\n</assembly>',
 True,
 False,
 None,
 None,
 None,
 'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\build\\视频批量转换工具\\视频批量转换工具.pkg',
 [('pyi-contents-directory _internal', '', 'OPTION'),
  ('PYZ-00.pyz',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\build\\视频批量转换工具\\PYZ-00.pyz',
   'PYZ'),
  ('struct',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\build\\视频批量转换工具\\localpycs\\struct.pyc',
   'PYMODULE'),
  ('pyimod01_archive',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\build\\视频批量转换工具\\localpycs\\pyimod01_archive.pyc',
   'PYMODULE'),
  ('pyimod02_importers',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\build\\视频批量转换工具\\localpycs\\pyimod02_importers.pyc',
   'PYMODULE'),
  ('pyimod03_ctypes',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\build\\视频批量转换工具\\localpycs\\pyimod03_ctypes.pyc',
   'PYMODULE'),
  ('pyimod04_pywin32',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\build\\视频批量转换工具\\localpycs\\pyimod04_pywin32.pyc',
   'PYMODULE'),
  ('pyiboot01_bootstrap',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\site-packages\\PyInstaller\\loader\\pyiboot01_bootstrap.py',
   'PYSOURCE'),
  ('pyi_rth_inspect',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_inspect.py',
   'PYSOURCE'),
  ('pyi_rth_pkgutil',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgutil.py',
   'PYSOURCE'),
  ('pyi_rth_multiprocessing',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_multiprocessing.py',
   'PYSOURCE'),
  ('pyi_rth_pyqt5',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pyqt5.py',
   'PYSOURCE'),
  ('gui', 'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\gui.py', 'PYSOURCE'),
  ('PyQt5\\Qt5\\bin\\Qt5Bluetooth.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Bluetooth.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Core.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Core.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5DBus.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5DBus.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Designer.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Designer.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Gui.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Gui.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Help.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Help.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Location.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Location.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Multimedia.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Multimedia.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5MultimediaWidgets.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5MultimediaWidgets.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Network.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Network.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Nfc.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Nfc.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5OpenGL.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5OpenGL.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Positioning.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Positioning.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5PositioningQuick.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5PositioningQuick.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5PrintSupport.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5PrintSupport.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Qml.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Qml.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5QmlModels.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5QmlModels.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5QmlWorkerScript.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5QmlWorkerScript.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Quick.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Quick.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Quick3D.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Quick3D.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Quick3DAssetImport.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Quick3DAssetImport.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Quick3DRender.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Quick3DRender.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Quick3DRuntimeRender.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Quick3DRuntimeRender.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Quick3DUtils.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Quick3DUtils.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5QuickControls2.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5QuickControls2.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5QuickParticles.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5QuickParticles.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5QuickShapes.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5QuickShapes.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5QuickTemplates2.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5QuickTemplates2.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5QuickTest.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5QuickTest.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5QuickWidgets.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5QuickWidgets.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5RemoteObjects.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5RemoteObjects.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Sensors.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Sensors.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5SerialPort.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5SerialPort.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Sql.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Sql.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Svg.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Svg.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Test.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Test.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5TextToSpeech.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5TextToSpeech.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5WebChannel.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5WebChannel.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5WebSockets.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5WebSockets.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5WebView.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5WebView.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Widgets.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Widgets.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5WinExtras.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5WinExtras.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Xml.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Xml.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5XmlPatterns.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5XmlPatterns.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\concrt140.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\concrt140.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\d3dcompiler_47.dll',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\d3dcompiler_47.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\libEGL.dll',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\libEGL.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\libGLESv2.dll',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\libGLESv2.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\libcrypto-1_1-x64.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\libcrypto-1_1-x64.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\libeay32.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\libeay32.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\libssl-1_1-x64.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\libssl-1_1-x64.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\msvcp140.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\msvcp140.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\msvcp140_1.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\msvcp140_1.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\msvcp140_2.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\msvcp140_2.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\opengl32sw.dll',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\opengl32sw.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\ssleay32.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\ssleay32.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\vcruntime140.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\vcruntime140.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\vcruntime140_1.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\vcruntime140_1.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\assetimporters\\assimp.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\assetimporters\\assimp.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\assetimporters\\uip.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\assetimporters\\uip.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\audio\\qtaudio_wasapi.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\audio\\qtaudio_wasapi.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\audio\\qtaudio_windows.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\audio\\qtaudio_windows.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\bearer\\qgenericbearer.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\bearer\\qgenericbearer.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\generic\\qtuiotouchplugin.dll',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\generic\\qtuiotouchplugin.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\geometryloaders\\defaultgeometryloader.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\geometryloaders\\defaultgeometryloader.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\geometryloaders\\gltfgeometryloader.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\geometryloaders\\gltfgeometryloader.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\geoservices\\qtgeoservices_esri.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\geoservices\\qtgeoservices_esri.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\geoservices\\qtgeoservices_itemsoverlay.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\geoservices\\qtgeoservices_itemsoverlay.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\geoservices\\qtgeoservices_mapbox.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\geoservices\\qtgeoservices_mapbox.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\geoservices\\qtgeoservices_nokia.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\geoservices\\qtgeoservices_nokia.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\geoservices\\qtgeoservices_osm.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\geoservices\\qtgeoservices_osm.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\iconengines\\qsvgicon.dll',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\iconengines\\qsvgicon.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qgif.dll',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qgif.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qicns.dll',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qicns.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qico.dll',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qico.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qjpeg.dll',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qjpeg.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qsvg.dll',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qsvg.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qtga.dll',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qtga.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qtiff.dll',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qtiff.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qwbmp.dll',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qwbmp.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qwebp.dll',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qwebp.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\mediaservice\\dsengine.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\mediaservice\\dsengine.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\mediaservice\\qtmedia_audioengine.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\mediaservice\\qtmedia_audioengine.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\mediaservice\\wmfengine.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\mediaservice\\wmfengine.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\platforms\\qminimal.dll',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\platforms\\qminimal.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\platforms\\qoffscreen.dll',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\platforms\\qoffscreen.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\platforms\\qwebgl.dll',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\platforms\\qwebgl.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\platforms\\qwindows.dll',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\platforms\\qwindows.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\platformthemes\\qxdgdesktopportal.dll',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\platformthemes\\qxdgdesktopportal.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\playlistformats\\qtmultimedia_m3u.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\playlistformats\\qtmultimedia_m3u.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\position\\qtposition_positionpoll.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\position\\qtposition_positionpoll.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\position\\qtposition_serialnmea.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\position\\qtposition_serialnmea.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\position\\qtposition_winrt.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\position\\qtposition_winrt.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\printsupport\\windowsprintersupport.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\printsupport\\windowsprintersupport.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\renderers\\openglrenderer.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\renderers\\openglrenderer.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\sceneparsers\\gltfsceneexport.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\sceneparsers\\gltfsceneexport.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\sceneparsers\\gltfsceneimport.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\sceneparsers\\gltfsceneimport.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\sensorgestures\\qtsensorgestures_plugin.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\sensorgestures\\qtsensorgestures_plugin.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\sensorgestures\\qtsensorgestures_shakeplugin.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\sensorgestures\\qtsensorgestures_shakeplugin.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\sensors\\qtsensors_generic.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\sensors\\qtsensors_generic.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\sqldrivers\\qsqlite.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\sqldrivers\\qsqlite.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\sqldrivers\\qsqlodbc.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\sqldrivers\\qsqlodbc.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\sqldrivers\\qsqlpsql.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\sqldrivers\\qsqlpsql.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\styles\\qwindowsvistastyle.dll',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\styles\\qwindowsvistastyle.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\texttospeech\\qtexttospeech_sapi.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\texttospeech\\qtexttospeech_sapi.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\webview\\qtwebview_webengine.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\webview\\qtwebview_webengine.dll',
   'BINARY'),
  ('ffmpeg\\ffmpeg.exe',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\ffmpeg\\ffmpeg.exe',
   'BINARY'),
  ('ffmpeg\\ffprobe.exe',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\ffmpeg\\ffprobe.exe',
   'BINARY'),
  ('python311.dll',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\python311.dll',
   'BINARY'),
  ('_decimal.pyd',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\DLLs\\_decimal.pyd',
   'EXTENSION'),
  ('_hashlib.pyd',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\DLLs\\_hashlib.pyd',
   'EXTENSION'),
  ('_lzma.pyd',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\DLLs\\_lzma.pyd',
   'EXTENSION'),
  ('_bz2.pyd',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\DLLs\\_bz2.pyd',
   'EXTENSION'),
  ('unicodedata.pyd',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\DLLs\\unicodedata.pyd',
   'EXTENSION'),
  ('select.pyd',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\DLLs\\select.pyd',
   'EXTENSION'),
  ('_socket.pyd',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\DLLs\\_socket.pyd',
   'EXTENSION'),
  ('_multiprocessing.pyd',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\DLLs\\_multiprocessing.pyd',
   'EXTENSION'),
  ('pyexpat.pyd',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\DLLs\\pyexpat.pyd',
   'EXTENSION'),
  ('_ssl.pyd',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\DLLs\\_ssl.pyd',
   'EXTENSION'),
  ('_ctypes.pyd',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\DLLs\\_ctypes.pyd',
   'EXTENSION'),
  ('_queue.pyd',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\DLLs\\_queue.pyd',
   'EXTENSION'),
  ('psutil\\_psutil_windows.pyd',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\site-packages\\psutil\\_psutil_windows.pyd',
   'EXTENSION'),
  ('PyQt5\\QtGui.pyd',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\site-packages\\PyQt5\\QtGui.pyd',
   'EXTENSION'),
  ('PyQt5\\sip.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\site-packages\\PyQt5\\sip.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('PyQt5\\QtCore.pyd',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\site-packages\\PyQt5\\QtCore.pyd',
   'EXTENSION'),
  ('PyQt5\\QtWidgets.pyd',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\site-packages\\PyQt5\\QtWidgets.pyd',
   'EXTENSION'),
  ('VCRUNTIME140.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\VCRUNTIME140.dll',
   'BINARY'),
  ('VCRUNTIME140_1.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\VCRUNTIME140_1.dll',
   'BINARY'),
  ('Qt5Core.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Core.dll',
   'BINARY'),
  ('api-ms-win-crt-runtime-l1-1-0.dll',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\api-ms-win-crt-runtime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-heap-l1-1-0.dll',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\api-ms-win-crt-heap-l1-1-0.dll',
   'BINARY'),
  ('MSVCP140.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\MSVCP140.dll',
   'BINARY'),
  ('api-ms-win-crt-utility-l1-1-0.dll',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\api-ms-win-crt-utility-l1-1-0.dll',
   'BINARY'),
  ('MSVCP140_1.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\MSVCP140_1.dll',
   'BINARY'),
  ('api-ms-win-crt-stdio-l1-1-0.dll',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\api-ms-win-crt-stdio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-environment-l1-1-0.dll',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\api-ms-win-crt-environment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-filesystem-l1-1-0.dll',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\api-ms-win-crt-filesystem-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-time-l1-1-0.dll',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\api-ms-win-crt-time-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-math-l1-1-0.dll',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\api-ms-win-crt-math-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-string-l1-1-0.dll',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\api-ms-win-crt-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-convert-l1-1-0.dll',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\api-ms-win-crt-convert-l1-1-0.dll',
   'BINARY'),
  ('Qt5Xml.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Xml.dll',
   'BINARY'),
  ('Qt5Gui.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Gui.dll',
   'BINARY'),
  ('Qt5Widgets.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Widgets.dll',
   'BINARY'),
  ('api-ms-win-crt-locale-l1-1-0.dll',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\api-ms-win-crt-locale-l1-1-0.dll',
   'BINARY'),
  ('Qt5Sql.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Sql.dll',
   'BINARY'),
  ('Qt5QmlModels.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5QmlModels.dll',
   'BINARY'),
  ('Qt5Positioning.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Positioning.dll',
   'BINARY'),
  ('Qt5PositioningQuick.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5PositioningQuick.dll',
   'BINARY'),
  ('Qt5Quick.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Quick.dll',
   'BINARY'),
  ('Qt5Qml.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Qml.dll',
   'BINARY'),
  ('Qt5Network.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Network.dll',
   'BINARY'),
  ('Qt5Multimedia.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Multimedia.dll',
   'BINARY'),
  ('Qt5Quick3DUtils.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Quick3DUtils.dll',
   'BINARY'),
  ('Qt5Quick3DRuntimeRender.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Quick3DRuntimeRender.dll',
   'BINARY'),
  ('Qt5Quick3DRender.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Quick3DRender.dll',
   'BINARY'),
  ('Qt5Quick3DAssetImport.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Quick3DAssetImport.dll',
   'BINARY'),
  ('Qt5QuickTemplates2.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5QuickTemplates2.dll',
   'BINARY'),
  ('Qt5Test.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Test.dll',
   'BINARY'),
  ('api-ms-win-crt-conio-l1-1-0.dll',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\api-ms-win-crt-conio-l1-1-0.dll',
   'BINARY'),
  ('libcrypto-1_1-x64.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\libcrypto-1_1-x64.dll',
   'BINARY'),
  ('LIBEAY32.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\LIBEAY32.dll',
   'BINARY'),
  ('LIBPQ.dll',
   'C:\\Users\\<USER>\\miniconda3\\Library\\bin\\LIBPQ.dll',
   'BINARY'),
  ('api-ms-win-crt-multibyte-l1-1-0.dll',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\api-ms-win-crt-multibyte-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-2-0.dll',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\api-ms-win-core-synch-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-crt-private-l1-1-0.dll',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\api-ms-win-crt-private-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-process-l1-1-0.dll',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\api-ms-win-crt-process-l1-1-0.dll',
   'BINARY'),
  ('zlib.dll', 'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\zlib.dll', 'BINARY'),
  ('libcrypto-3-x64.dll',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Library\\bin\\libcrypto-3-x64.dll',
   'BINARY'),
  ('liblzma.dll',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Library\\bin\\liblzma.dll',
   'BINARY'),
  ('LIBBZ2.dll',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Library\\bin\\LIBBZ2.dll',
   'BINARY'),
  ('libssl-3-x64.dll',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Library\\bin\\libssl-3-x64.dll',
   'BINARY'),
  ('ffi.dll',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Library\\bin\\ffi.dll',
   'BINARY'),
  ('python3.dll',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\python3.dll',
   'BINARY'),
  ('ucrtbase.dll',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\ucrtbase.dll',
   'BINARY'),
  ('gssapi64.dll',
   'C:\\Users\\<USER>\\miniconda3\\Library\\bin\\gssapi64.dll',
   'BINARY'),
  ('api-ms-win-core-rtlsupport-l1-1-0.dll',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\api-ms-win-core-rtlsupport-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-console-l1-1-0.dll',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\api-ms-win-core-console-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-profile-l1-1-0.dll',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\api-ms-win-core-profile-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-1.dll',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\api-ms-win-core-processthreads-l1-1-1.dll',
   'BINARY'),
  ('api-ms-win-core-localization-l1-2-0.dll',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\api-ms-win-core-localization-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l2-1-0.dll',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\api-ms-win-core-file-l2-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processenvironment-l1-1-0.dll',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\api-ms-win-core-processenvironment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-debug-l1-1-0.dll',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\api-ms-win-core-debug-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-namedpipe-l1-1-0.dll',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\api-ms-win-core-namedpipe-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-libraryloader-l1-1-0.dll',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\api-ms-win-core-libraryloader-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-util-l1-1-0.dll',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\api-ms-win-core-util-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-timezone-l1-1-0.dll',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\api-ms-win-core-timezone-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-heap-l1-1-0.dll',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\api-ms-win-core-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-2-0.dll',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\api-ms-win-core-file-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-string-l1-1-0.dll',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\api-ms-win-core-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-handle-l1-1-0.dll',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\api-ms-win-core-handle-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-datetime-l1-1-0.dll',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\api-ms-win-core-datetime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-1-0.dll',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\api-ms-win-core-synch-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-0.dll',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\api-ms-win-core-processthreads-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-interlocked-l1-1-0.dll',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\api-ms-win-core-interlocked-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-errorhandling-l1-1-0.dll',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\api-ms-win-core-errorhandling-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-fibers-l1-1-0.dll',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\api-ms-win-core-fibers-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-memory-l1-1-0.dll',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\api-ms-win-core-memory-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-1-0.dll',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\api-ms-win-core-file-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-sysinfo-l1-1-0.dll',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\api-ms-win-core-sysinfo-l1-1-0.dll',
   'BINARY'),
  ('comerr64.dll',
   'C:\\Users\\<USER>\\miniconda3\\Library\\bin\\comerr64.dll',
   'BINARY'),
  ('krb5_64.dll',
   'C:\\Users\\<USER>\\miniconda3\\Library\\bin\\krb5_64.dll',
   'BINARY'),
  ('k5sprt64.dll',
   'C:\\Users\\<USER>\\miniconda3\\Library\\bin\\k5sprt64.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\translations\\qt_help_sk.qm',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_sk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_pt.qm',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_pt.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_ko.qm',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_ko.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_lv.qm',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_lv.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_fa.qm',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_fa.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_sl.qm',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_sl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_sk.qm',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_sk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_hu.qm',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_hu.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_da.qm',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_da.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_fi.qm',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_fi.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_de.qm',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_de.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_gl.qm',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_gl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_es.qm',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_es.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_hu.qm',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_hu.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_da.qm',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_da.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_fi.qm',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_fi.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_it.qm',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_it.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_pl.qm',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_pl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_ru.qm',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_ru.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_uk.qm',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_uk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_sv.qm',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_sv.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_tr.qm',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_tr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_fr.qm',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_fr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_ja.qm',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_ja.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_he.qm',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_he.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_gl.qm',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_gl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_ru.qm',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_ru.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_ca.qm',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_ca.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_bg.qm',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_bg.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_ru.qm',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_ru.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_gd.qm',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_gd.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_zh_TW.qm',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_zh_TW.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_ar.qm',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_ar.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_de.qm',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_de.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_he.qm',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_he.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_ko.qm',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_ko.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_fr.qm',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_fr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_zh_CN.qm',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_zh_CN.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_en.qm',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_en.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_de.qm',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_de.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_ja.qm',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_ja.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_cs.qm',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_cs.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_fr.qm',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_fr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_es.qm',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_es.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_ca.qm',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_ca.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_tr.qm',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_tr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_es.qm',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_es.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_cs.qm',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_cs.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_uk.qm',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_uk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_gd.qm',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_gd.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_ar.qm',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_ar.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_en.qm',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_en.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_it.qm',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_it.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_sl.qm',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_sl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_bg.qm',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_bg.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_uk.qm',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_uk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_pl.qm',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_pl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_zh_CN.qm',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_zh_CN.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_it.qm',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_it.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_ja.qm',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_ja.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_en.qm',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_en.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_lt.qm',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_lt.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_pl.qm',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_pl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_bg.qm',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_bg.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_cs.qm',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_cs.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_da.qm',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_da.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_tr.qm',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_tr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_lv.qm',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_lv.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_ar.qm',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_ar.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_ca.qm',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_ca.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_sk.qm',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_sk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_hu.qm',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_hu.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_zh_TW.qm',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_zh_TW.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_zh_TW.qm',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_zh_TW.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_ko.qm',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_ko.qm',
   'DATA'),
  ('base_library.zip',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\build\\视频批量转换工具\\base_library.zip',
   'DATA')],
 [],
 False,
 False,
 1750162602,
 [('runw.exe',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\site-packages\\PyInstaller\\bootloader\\Windows-64bit-intel\\runw.exe',
   'EXECUTABLE')],
 'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\python311.dll')
