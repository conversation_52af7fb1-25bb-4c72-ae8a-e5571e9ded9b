(['C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\gui.py'],
 ['C:\\Users\\<USER>\\Music\\批量视频转换-GUI'],
 [],
 [('C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\site-packages\\numpy\\_pyinstaller',
   0),
  ('C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\site-packages\\playwright\\_impl\\__pyinstaller',
   0),
  ('C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\site-packages\\pypinyin\\__pyinstaller',
   0),
  ('C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks',
   -1000),
  ('C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\site-packages\\_pyinstaller_hooks_contrib',
   -1000)],
 {},
 [],
 [],
 False,
 {},
 0,
 [],
 [('PyQt5\\Qt5\\bin\\Qt5Bluetooth.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Bluetooth.dll',
   'DATA'),
  ('PyQt5\\Qt5\\bin\\Qt5Core.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Core.dll',
   'DATA'),
  ('PyQt5\\Qt5\\bin\\Qt5DBus.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5DBus.dll',
   'DATA'),
  ('PyQt5\\Qt5\\bin\\Qt5Designer.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Designer.dll',
   'DATA'),
  ('PyQt5\\Qt5\\bin\\Qt5Gui.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Gui.dll',
   'DATA'),
  ('PyQt5\\Qt5\\bin\\Qt5Help.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Help.dll',
   'DATA'),
  ('PyQt5\\Qt5\\bin\\Qt5Location.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Location.dll',
   'DATA'),
  ('PyQt5\\Qt5\\bin\\Qt5Multimedia.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Multimedia.dll',
   'DATA'),
  ('PyQt5\\Qt5\\bin\\Qt5MultimediaWidgets.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5MultimediaWidgets.dll',
   'DATA'),
  ('PyQt5\\Qt5\\bin\\Qt5Network.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Network.dll',
   'DATA'),
  ('PyQt5\\Qt5\\bin\\Qt5Nfc.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Nfc.dll',
   'DATA'),
  ('PyQt5\\Qt5\\bin\\Qt5OpenGL.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5OpenGL.dll',
   'DATA'),
  ('PyQt5\\Qt5\\bin\\Qt5Positioning.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Positioning.dll',
   'DATA'),
  ('PyQt5\\Qt5\\bin\\Qt5PositioningQuick.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5PositioningQuick.dll',
   'DATA'),
  ('PyQt5\\Qt5\\bin\\Qt5PrintSupport.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5PrintSupport.dll',
   'DATA'),
  ('PyQt5\\Qt5\\bin\\Qt5Qml.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Qml.dll',
   'DATA'),
  ('PyQt5\\Qt5\\bin\\Qt5QmlModels.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5QmlModels.dll',
   'DATA'),
  ('PyQt5\\Qt5\\bin\\Qt5QmlWorkerScript.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5QmlWorkerScript.dll',
   'DATA'),
  ('PyQt5\\Qt5\\bin\\Qt5Quick.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Quick.dll',
   'DATA'),
  ('PyQt5\\Qt5\\bin\\Qt5Quick3D.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Quick3D.dll',
   'DATA'),
  ('PyQt5\\Qt5\\bin\\Qt5Quick3DAssetImport.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Quick3DAssetImport.dll',
   'DATA'),
  ('PyQt5\\Qt5\\bin\\Qt5Quick3DRender.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Quick3DRender.dll',
   'DATA'),
  ('PyQt5\\Qt5\\bin\\Qt5Quick3DRuntimeRender.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Quick3DRuntimeRender.dll',
   'DATA'),
  ('PyQt5\\Qt5\\bin\\Qt5Quick3DUtils.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Quick3DUtils.dll',
   'DATA'),
  ('PyQt5\\Qt5\\bin\\Qt5QuickControls2.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5QuickControls2.dll',
   'DATA'),
  ('PyQt5\\Qt5\\bin\\Qt5QuickParticles.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5QuickParticles.dll',
   'DATA'),
  ('PyQt5\\Qt5\\bin\\Qt5QuickShapes.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5QuickShapes.dll',
   'DATA'),
  ('PyQt5\\Qt5\\bin\\Qt5QuickTemplates2.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5QuickTemplates2.dll',
   'DATA'),
  ('PyQt5\\Qt5\\bin\\Qt5QuickTest.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5QuickTest.dll',
   'DATA'),
  ('PyQt5\\Qt5\\bin\\Qt5QuickWidgets.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5QuickWidgets.dll',
   'DATA'),
  ('PyQt5\\Qt5\\bin\\Qt5RemoteObjects.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5RemoteObjects.dll',
   'DATA'),
  ('PyQt5\\Qt5\\bin\\Qt5Sensors.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Sensors.dll',
   'DATA'),
  ('PyQt5\\Qt5\\bin\\Qt5SerialPort.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5SerialPort.dll',
   'DATA'),
  ('PyQt5\\Qt5\\bin\\Qt5Sql.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Sql.dll',
   'DATA'),
  ('PyQt5\\Qt5\\bin\\Qt5Svg.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Svg.dll',
   'DATA'),
  ('PyQt5\\Qt5\\bin\\Qt5Test.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Test.dll',
   'DATA'),
  ('PyQt5\\Qt5\\bin\\Qt5TextToSpeech.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5TextToSpeech.dll',
   'DATA'),
  ('PyQt5\\Qt5\\bin\\Qt5WebChannel.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5WebChannel.dll',
   'DATA'),
  ('PyQt5\\Qt5\\bin\\Qt5WebSockets.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5WebSockets.dll',
   'DATA'),
  ('PyQt5\\Qt5\\bin\\Qt5WebView.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5WebView.dll',
   'DATA'),
  ('PyQt5\\Qt5\\bin\\Qt5Widgets.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Widgets.dll',
   'DATA'),
  ('PyQt5\\Qt5\\bin\\Qt5WinExtras.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5WinExtras.dll',
   'DATA'),
  ('PyQt5\\Qt5\\bin\\Qt5Xml.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Xml.dll',
   'DATA'),
  ('PyQt5\\Qt5\\bin\\Qt5XmlPatterns.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5XmlPatterns.dll',
   'DATA'),
  ('PyQt5\\Qt5\\bin\\concrt140.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\concrt140.dll',
   'DATA'),
  ('PyQt5\\Qt5\\bin\\d3dcompiler_47.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\d3dcompiler_47.dll',
   'DATA'),
  ('PyQt5\\Qt5\\bin\\libEGL.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\libEGL.dll',
   'DATA'),
  ('PyQt5\\Qt5\\bin\\libGLESv2.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\libGLESv2.dll',
   'DATA'),
  ('PyQt5\\Qt5\\bin\\libcrypto-1_1-x64.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\libcrypto-1_1-x64.dll',
   'DATA'),
  ('PyQt5\\Qt5\\bin\\libeay32.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\libeay32.dll',
   'DATA'),
  ('PyQt5\\Qt5\\bin\\libssl-1_1-x64.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\libssl-1_1-x64.dll',
   'DATA'),
  ('PyQt5\\Qt5\\bin\\msvcp140.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\msvcp140.dll',
   'DATA'),
  ('PyQt5\\Qt5\\bin\\msvcp140_1.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\msvcp140_1.dll',
   'DATA'),
  ('PyQt5\\Qt5\\bin\\msvcp140_2.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\msvcp140_2.dll',
   'DATA'),
  ('PyQt5\\Qt5\\bin\\opengl32sw.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\opengl32sw.dll',
   'DATA'),
  ('PyQt5\\Qt5\\bin\\ssleay32.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\ssleay32.dll',
   'DATA'),
  ('PyQt5\\Qt5\\bin\\vcruntime140.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\vcruntime140.dll',
   'DATA'),
  ('PyQt5\\Qt5\\bin\\vcruntime140_1.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\vcruntime140_1.dll',
   'DATA'),
  ('PyQt5\\Qt5\\plugins\\assetimporters\\assimp.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\assetimporters\\assimp.dll',
   'DATA'),
  ('PyQt5\\Qt5\\plugins\\assetimporters\\uip.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\assetimporters\\uip.dll',
   'DATA'),
  ('PyQt5\\Qt5\\plugins\\audio\\qtaudio_wasapi.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\audio\\qtaudio_wasapi.dll',
   'DATA'),
  ('PyQt5\\Qt5\\plugins\\audio\\qtaudio_windows.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\audio\\qtaudio_windows.dll',
   'DATA'),
  ('PyQt5\\Qt5\\plugins\\bearer\\qgenericbearer.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\bearer\\qgenericbearer.dll',
   'DATA'),
  ('PyQt5\\Qt5\\plugins\\generic\\qtuiotouchplugin.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\generic\\qtuiotouchplugin.dll',
   'DATA'),
  ('PyQt5\\Qt5\\plugins\\geometryloaders\\defaultgeometryloader.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\geometryloaders\\defaultgeometryloader.dll',
   'DATA'),
  ('PyQt5\\Qt5\\plugins\\geometryloaders\\gltfgeometryloader.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\geometryloaders\\gltfgeometryloader.dll',
   'DATA'),
  ('PyQt5\\Qt5\\plugins\\geoservices\\qtgeoservices_esri.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\geoservices\\qtgeoservices_esri.dll',
   'DATA'),
  ('PyQt5\\Qt5\\plugins\\geoservices\\qtgeoservices_itemsoverlay.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\geoservices\\qtgeoservices_itemsoverlay.dll',
   'DATA'),
  ('PyQt5\\Qt5\\plugins\\geoservices\\qtgeoservices_mapbox.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\geoservices\\qtgeoservices_mapbox.dll',
   'DATA'),
  ('PyQt5\\Qt5\\plugins\\geoservices\\qtgeoservices_nokia.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\geoservices\\qtgeoservices_nokia.dll',
   'DATA'),
  ('PyQt5\\Qt5\\plugins\\geoservices\\qtgeoservices_osm.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\geoservices\\qtgeoservices_osm.dll',
   'DATA'),
  ('PyQt5\\Qt5\\plugins\\iconengines\\qsvgicon.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\iconengines\\qsvgicon.dll',
   'DATA'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qgif.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qgif.dll',
   'DATA'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qicns.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qicns.dll',
   'DATA'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qico.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qico.dll',
   'DATA'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qjpeg.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qjpeg.dll',
   'DATA'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qsvg.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qsvg.dll',
   'DATA'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qtga.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qtga.dll',
   'DATA'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qtiff.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qtiff.dll',
   'DATA'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qwbmp.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qwbmp.dll',
   'DATA'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qwebp.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qwebp.dll',
   'DATA'),
  ('PyQt5\\Qt5\\plugins\\mediaservice\\dsengine.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\mediaservice\\dsengine.dll',
   'DATA'),
  ('PyQt5\\Qt5\\plugins\\mediaservice\\qtmedia_audioengine.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\mediaservice\\qtmedia_audioengine.dll',
   'DATA'),
  ('PyQt5\\Qt5\\plugins\\mediaservice\\wmfengine.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\mediaservice\\wmfengine.dll',
   'DATA'),
  ('PyQt5\\Qt5\\plugins\\platforms\\qminimal.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\platforms\\qminimal.dll',
   'DATA'),
  ('PyQt5\\Qt5\\plugins\\platforms\\qoffscreen.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\platforms\\qoffscreen.dll',
   'DATA'),
  ('PyQt5\\Qt5\\plugins\\platforms\\qwebgl.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\platforms\\qwebgl.dll',
   'DATA'),
  ('PyQt5\\Qt5\\plugins\\platforms\\qwindows.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\platforms\\qwindows.dll',
   'DATA'),
  ('PyQt5\\Qt5\\plugins\\platformthemes\\qxdgdesktopportal.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\platformthemes\\qxdgdesktopportal.dll',
   'DATA'),
  ('PyQt5\\Qt5\\plugins\\playlistformats\\qtmultimedia_m3u.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\playlistformats\\qtmultimedia_m3u.dll',
   'DATA'),
  ('PyQt5\\Qt5\\plugins\\position\\qtposition_positionpoll.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\position\\qtposition_positionpoll.dll',
   'DATA'),
  ('PyQt5\\Qt5\\plugins\\position\\qtposition_serialnmea.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\position\\qtposition_serialnmea.dll',
   'DATA'),
  ('PyQt5\\Qt5\\plugins\\position\\qtposition_winrt.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\position\\qtposition_winrt.dll',
   'DATA'),
  ('PyQt5\\Qt5\\plugins\\printsupport\\windowsprintersupport.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\printsupport\\windowsprintersupport.dll',
   'DATA'),
  ('PyQt5\\Qt5\\plugins\\renderers\\openglrenderer.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\renderers\\openglrenderer.dll',
   'DATA'),
  ('PyQt5\\Qt5\\plugins\\sceneparsers\\gltfsceneexport.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\sceneparsers\\gltfsceneexport.dll',
   'DATA'),
  ('PyQt5\\Qt5\\plugins\\sceneparsers\\gltfsceneimport.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\sceneparsers\\gltfsceneimport.dll',
   'DATA'),
  ('PyQt5\\Qt5\\plugins\\sensorgestures\\qtsensorgestures_plugin.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\sensorgestures\\qtsensorgestures_plugin.dll',
   'DATA'),
  ('PyQt5\\Qt5\\plugins\\sensorgestures\\qtsensorgestures_shakeplugin.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\sensorgestures\\qtsensorgestures_shakeplugin.dll',
   'DATA'),
  ('PyQt5\\Qt5\\plugins\\sensors\\qtsensors_generic.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\sensors\\qtsensors_generic.dll',
   'DATA'),
  ('PyQt5\\Qt5\\plugins\\sqldrivers\\qsqlite.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\sqldrivers\\qsqlite.dll',
   'DATA'),
  ('PyQt5\\Qt5\\plugins\\sqldrivers\\qsqlodbc.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\sqldrivers\\qsqlodbc.dll',
   'DATA'),
  ('PyQt5\\Qt5\\plugins\\sqldrivers\\qsqlpsql.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\sqldrivers\\qsqlpsql.dll',
   'DATA'),
  ('PyQt5\\Qt5\\plugins\\styles\\qwindowsvistastyle.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\styles\\qwindowsvistastyle.dll',
   'DATA'),
  ('PyQt5\\Qt5\\plugins\\texttospeech\\qtexttospeech_sapi.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\texttospeech\\qtexttospeech_sapi.dll',
   'DATA'),
  ('PyQt5\\Qt5\\plugins\\webview\\qtwebview_webengine.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\webview\\qtwebview_webengine.dll',
   'DATA'),
  ('ffmpeg\\ffmpeg.exe',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\ffmpeg\\ffmpeg.exe',
   'DATA'),
  ('ffmpeg\\ffprobe.exe',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\ffmpeg\\ffprobe.exe',
   'DATA')],
 '3.11.11 | packaged by Anaconda, Inc. | (main, Dec 11 2024, 16:34:19) [MSC '
 'v.1929 64 bit (AMD64)]',
 [('pyi_rth_inspect',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_inspect.py',
   'PYSOURCE'),
  ('pyi_rth_pkgutil',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgutil.py',
   'PYSOURCE'),
  ('pyi_rth_multiprocessing',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_multiprocessing.py',
   'PYSOURCE'),
  ('pyi_rth_pyqt5',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pyqt5.py',
   'PYSOURCE'),
  ('gui', 'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\gui.py', 'PYSOURCE')],
 [('_pyi_rth_utils.qt',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\site-packages\\PyInstaller\\fake-modules\\_pyi_rth_utils\\qt.py',
   'PYMODULE'),
  ('importlib',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\importlib\\__init__.py',
   'PYMODULE'),
  ('importlib.machinery',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.metadata',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\importlib\\metadata\\__init__.py',
   'PYMODULE'),
  ('typing',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\typing.py',
   'PYMODULE'),
  ('importlib.abc',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\importlib\\abc.py',
   'PYMODULE'),
  ('importlib.resources.abc',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\importlib\\resources\\abc.py',
   'PYMODULE'),
  ('importlib.resources',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\importlib\\resources\\__init__.py',
   'PYMODULE'),
  ('importlib.resources._legacy',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\importlib\\resources\\_legacy.py',
   'PYMODULE'),
  ('importlib.resources._common',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\importlib\\resources\\_common.py',
   'PYMODULE'),
  ('importlib.resources._adapters',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\importlib\\resources\\_adapters.py',
   'PYMODULE'),
  ('tempfile',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\tempfile.py',
   'PYMODULE'),
  ('random',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\random.py',
   'PYMODULE'),
  ('statistics',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\statistics.py',
   'PYMODULE'),
  ('decimal',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\decimal.py',
   'PYMODULE'),
  ('_pydecimal',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\_pydecimal.py',
   'PYMODULE'),
  ('contextvars',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\contextvars.py',
   'PYMODULE'),
  ('fractions',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\fractions.py',
   'PYMODULE'),
  ('numbers',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\numbers.py',
   'PYMODULE'),
  ('hashlib',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\hashlib.py',
   'PYMODULE'),
  ('logging',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\logging\\__init__.py',
   'PYMODULE'),
  ('pickle',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\pickle.py',
   'PYMODULE'),
  ('pprint',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\pprint.py',
   'PYMODULE'),
  ('dataclasses',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\dataclasses.py',
   'PYMODULE'),
  ('inspect',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\inspect.py',
   'PYMODULE'),
  ('argparse',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\argparse.py',
   'PYMODULE'),
  ('gettext',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\gettext.py',
   'PYMODULE'),
  ('token',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\token.py',
   'PYMODULE'),
  ('dis', 'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\dis.py', 'PYMODULE'),
  ('opcode',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\opcode.py',
   'PYMODULE'),
  ('ast', 'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\ast.py', 'PYMODULE'),
  ('copy', 'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\copy.py', 'PYMODULE'),
  ('_compat_pickle',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\_compat_pickle.py',
   'PYMODULE'),
  ('struct',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\struct.py',
   'PYMODULE'),
  ('string',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\string.py',
   'PYMODULE'),
  ('bisect',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\bisect.py',
   'PYMODULE'),
  ('shutil',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\shutil.py',
   'PYMODULE'),
  ('tarfile',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\tarfile.py',
   'PYMODULE'),
  ('gzip', 'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\gzip.py', 'PYMODULE'),
  ('_compression',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\_compression.py',
   'PYMODULE'),
  ('lzma', 'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\lzma.py', 'PYMODULE'),
  ('bz2', 'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\bz2.py', 'PYMODULE'),
  ('fnmatch',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\fnmatch.py',
   'PYMODULE'),
  ('importlib._abc',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\importlib\\_abc.py',
   'PYMODULE'),
  ('contextlib',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\contextlib.py',
   'PYMODULE'),
  ('importlib.metadata._itertools',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\importlib\\metadata\\_itertools.py',
   'PYMODULE'),
  ('importlib.metadata._functools',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\importlib\\metadata\\_functools.py',
   'PYMODULE'),
  ('importlib.metadata._collections',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\importlib\\metadata\\_collections.py',
   'PYMODULE'),
  ('importlib.metadata._meta',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\importlib\\metadata\\_meta.py',
   'PYMODULE'),
  ('importlib.metadata._adapters',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\importlib\\metadata\\_adapters.py',
   'PYMODULE'),
  ('importlib.metadata._text',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\importlib\\metadata\\_text.py',
   'PYMODULE'),
  ('email.message',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\email\\message.py',
   'PYMODULE'),
  ('email.policy',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\email\\policy.py',
   'PYMODULE'),
  ('email.contentmanager',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.quoprimime',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\email\\quoprimime.py',
   'PYMODULE'),
  ('email.headerregistry',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email._header_value_parser',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('urllib',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\urllib\\__init__.py',
   'PYMODULE'),
  ('email.iterators',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\email\\iterators.py',
   'PYMODULE'),
  ('email.generator',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\email\\generator.py',
   'PYMODULE'),
  ('email._encoded_words',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('base64',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\base64.py',
   'PYMODULE'),
  ('getopt',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\getopt.py',
   'PYMODULE'),
  ('email.charset',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\email\\charset.py',
   'PYMODULE'),
  ('email.encoders',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\email\\encoders.py',
   'PYMODULE'),
  ('email.base64mime',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\email\\base64mime.py',
   'PYMODULE'),
  ('email._policybase',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\email\\_policybase.py',
   'PYMODULE'),
  ('email.header',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\email\\header.py',
   'PYMODULE'),
  ('email.errors',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\email\\errors.py',
   'PYMODULE'),
  ('email.utils',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\email\\utils.py',
   'PYMODULE'),
  ('email._parseaddr',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\email\\_parseaddr.py',
   'PYMODULE'),
  ('calendar',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\calendar.py',
   'PYMODULE'),
  ('urllib.parse',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\urllib\\parse.py',
   'PYMODULE'),
  ('ipaddress',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\ipaddress.py',
   'PYMODULE'),
  ('datetime',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\datetime.py',
   'PYMODULE'),
  ('_strptime',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\_strptime.py',
   'PYMODULE'),
  ('socket',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\socket.py',
   'PYMODULE'),
  ('selectors',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\selectors.py',
   'PYMODULE'),
  ('quopri',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\quopri.py',
   'PYMODULE'),
  ('textwrap',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\textwrap.py',
   'PYMODULE'),
  ('zipfile',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\zipfile.py',
   'PYMODULE'),
  ('py_compile',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\py_compile.py',
   'PYMODULE'),
  ('importlib.util',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\importlib\\util.py',
   'PYMODULE'),
  ('email',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\email\\__init__.py',
   'PYMODULE'),
  ('email.parser',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\email\\parser.py',
   'PYMODULE'),
  ('email.feedparser',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\email\\feedparser.py',
   'PYMODULE'),
  ('csv', 'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\csv.py', 'PYMODULE'),
  ('importlib.readers',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\importlib\\readers.py',
   'PYMODULE'),
  ('importlib.resources.readers',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\importlib\\resources\\readers.py',
   'PYMODULE'),
  ('importlib.resources._itertools',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\importlib\\resources\\_itertools.py',
   'PYMODULE'),
  ('tokenize',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\tokenize.py',
   'PYMODULE'),
  ('importlib._bootstrap',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('_pyi_rth_utils',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\site-packages\\PyInstaller\\fake-modules\\_pyi_rth_utils\\__init__.py',
   'PYMODULE'),
  ('subprocess',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\subprocess.py',
   'PYMODULE'),
  ('signal',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\signal.py',
   'PYMODULE'),
  ('multiprocessing.spawn',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\multiprocessing\\spawn.py',
   'PYMODULE'),
  ('multiprocessing.resource_tracker',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\multiprocessing\\resource_tracker.py',
   'PYMODULE'),
  ('multiprocessing.util',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\multiprocessing\\util.py',
   'PYMODULE'),
  ('multiprocessing.forkserver',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\multiprocessing\\forkserver.py',
   'PYMODULE'),
  ('multiprocessing.connection',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\multiprocessing\\connection.py',
   'PYMODULE'),
  ('multiprocessing.resource_sharer',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\multiprocessing\\resource_sharer.py',
   'PYMODULE'),
  ('xmlrpc.client',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\xmlrpc\\client.py',
   'PYMODULE'),
  ('xmlrpc',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\xmlrpc\\__init__.py',
   'PYMODULE'),
  ('xml.parsers.expat',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\xml\\parsers\\expat.py',
   'PYMODULE'),
  ('xml.parsers',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\xml\\parsers\\__init__.py',
   'PYMODULE'),
  ('xml',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\xml\\__init__.py',
   'PYMODULE'),
  ('xml.sax.expatreader',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\xml\\sax\\expatreader.py',
   'PYMODULE'),
  ('xml.sax.saxutils',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\xml\\sax\\saxutils.py',
   'PYMODULE'),
  ('urllib.request',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\urllib\\request.py',
   'PYMODULE'),
  ('getpass',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\getpass.py',
   'PYMODULE'),
  ('nturl2path',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\nturl2path.py',
   'PYMODULE'),
  ('ftplib',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\ftplib.py',
   'PYMODULE'),
  ('netrc',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\netrc.py',
   'PYMODULE'),
  ('shlex',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\shlex.py',
   'PYMODULE'),
  ('mimetypes',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\mimetypes.py',
   'PYMODULE'),
  ('http.cookiejar',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\http\\cookiejar.py',
   'PYMODULE'),
  ('http',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\http\\__init__.py',
   'PYMODULE'),
  ('ssl', 'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\ssl.py', 'PYMODULE'),
  ('urllib.response',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\urllib\\response.py',
   'PYMODULE'),
  ('urllib.error',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\urllib\\error.py',
   'PYMODULE'),
  ('xml.sax',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\xml\\sax\\__init__.py',
   'PYMODULE'),
  ('xml.sax.handler',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\xml\\sax\\handler.py',
   'PYMODULE'),
  ('xml.sax._exceptions',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\xml\\sax\\_exceptions.py',
   'PYMODULE'),
  ('xml.sax.xmlreader',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\xml\\sax\\xmlreader.py',
   'PYMODULE'),
  ('http.client',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\http\\client.py',
   'PYMODULE'),
  ('hmac', 'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\hmac.py', 'PYMODULE'),
  ('multiprocessing.context',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\multiprocessing\\context.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_win32',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\multiprocessing\\popen_spawn_win32.py',
   'PYMODULE'),
  ('multiprocessing.popen_forkserver',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\multiprocessing\\popen_forkserver.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_posix',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\multiprocessing\\popen_spawn_posix.py',
   'PYMODULE'),
  ('multiprocessing.popen_fork',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\multiprocessing\\popen_fork.py',
   'PYMODULE'),
  ('multiprocessing.sharedctypes',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\multiprocessing\\sharedctypes.py',
   'PYMODULE'),
  ('multiprocessing.heap',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\multiprocessing\\heap.py',
   'PYMODULE'),
  ('ctypes',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\ctypes\\__init__.py',
   'PYMODULE'),
  ('ctypes._endian',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\ctypes\\_endian.py',
   'PYMODULE'),
  ('multiprocessing.pool',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\multiprocessing\\pool.py',
   'PYMODULE'),
  ('multiprocessing.dummy',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\multiprocessing\\dummy\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.dummy.connection',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\multiprocessing\\dummy\\connection.py',
   'PYMODULE'),
  ('queue',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\queue.py',
   'PYMODULE'),
  ('multiprocessing.queues',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\multiprocessing\\queues.py',
   'PYMODULE'),
  ('multiprocessing.synchronize',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\multiprocessing\\synchronize.py',
   'PYMODULE'),
  ('multiprocessing.managers',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\multiprocessing\\managers.py',
   'PYMODULE'),
  ('multiprocessing.shared_memory',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\multiprocessing\\shared_memory.py',
   'PYMODULE'),
  ('secrets',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\secrets.py',
   'PYMODULE'),
  ('multiprocessing.reduction',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\multiprocessing\\reduction.py',
   'PYMODULE'),
  ('multiprocessing.process',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\multiprocessing\\process.py',
   'PYMODULE'),
  ('runpy',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\runpy.py',
   'PYMODULE'),
  ('pkgutil',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\pkgutil.py',
   'PYMODULE'),
  ('zipimport',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\zipimport.py',
   'PYMODULE'),
  ('multiprocessing',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\multiprocessing\\__init__.py',
   'PYMODULE'),
  ('tracemalloc',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\tracemalloc.py',
   'PYMODULE'),
  ('_py_abc',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\_py_abc.py',
   'PYMODULE'),
  ('stringprep',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\stringprep.py',
   'PYMODULE'),
  ('threading',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\threading.py',
   'PYMODULE'),
  ('_threading_local',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\_threading_local.py',
   'PYMODULE'),
  ('script', 'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\script.py', 'PYMODULE'),
  ('ffmpeg_utils',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\ffmpeg_utils.py',
   'PYMODULE'),
  ('video_utils',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\video_utils.py',
   'PYMODULE'),
  ('psutil',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\site-packages\\psutil\\__init__.py',
   'PYMODULE'),
  ('psutil._pswindows',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\site-packages\\psutil\\_pswindows.py',
   'PYMODULE'),
  ('psutil._common',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\site-packages\\psutil\\_common.py',
   'PYMODULE'),
  ('concurrent.futures',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\concurrent\\futures\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures.thread',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\concurrent\\futures\\thread.py',
   'PYMODULE'),
  ('concurrent.futures.process',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\concurrent\\futures\\process.py',
   'PYMODULE'),
  ('concurrent.futures._base',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\concurrent\\futures\\_base.py',
   'PYMODULE'),
  ('concurrent',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\concurrent\\__init__.py',
   'PYMODULE'),
  ('PyQt5',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\site-packages\\PyQt5\\__init__.py',
   'PYMODULE'),
  ('pathlib',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\pathlib.py',
   'PYMODULE'),
  ('json',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\json\\__init__.py',
   'PYMODULE'),
  ('json.encoder',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\json\\encoder.py',
   'PYMODULE'),
  ('json.decoder',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\json\\decoder.py',
   'PYMODULE'),
  ('json.scanner',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\json\\scanner.py',
   'PYMODULE')],
 [('PyQt5\\Qt5\\bin\\Qt5Bluetooth.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Bluetooth.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Core.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Core.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5DBus.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5DBus.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Designer.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Designer.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Gui.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Gui.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Help.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Help.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Location.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Location.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Multimedia.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Multimedia.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5MultimediaWidgets.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5MultimediaWidgets.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Network.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Network.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Nfc.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Nfc.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5OpenGL.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5OpenGL.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Positioning.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Positioning.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5PositioningQuick.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5PositioningQuick.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5PrintSupport.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5PrintSupport.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Qml.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Qml.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5QmlModels.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5QmlModels.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5QmlWorkerScript.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5QmlWorkerScript.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Quick.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Quick.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Quick3D.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Quick3D.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Quick3DAssetImport.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Quick3DAssetImport.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Quick3DRender.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Quick3DRender.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Quick3DRuntimeRender.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Quick3DRuntimeRender.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Quick3DUtils.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Quick3DUtils.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5QuickControls2.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5QuickControls2.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5QuickParticles.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5QuickParticles.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5QuickShapes.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5QuickShapes.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5QuickTemplates2.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5QuickTemplates2.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5QuickTest.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5QuickTest.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5QuickWidgets.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5QuickWidgets.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5RemoteObjects.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5RemoteObjects.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Sensors.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Sensors.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5SerialPort.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5SerialPort.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Sql.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Sql.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Svg.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Svg.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Test.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Test.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5TextToSpeech.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5TextToSpeech.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5WebChannel.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5WebChannel.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5WebSockets.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5WebSockets.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5WebView.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5WebView.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Widgets.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Widgets.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5WinExtras.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5WinExtras.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5Xml.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Xml.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\Qt5XmlPatterns.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5XmlPatterns.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\concrt140.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\concrt140.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\d3dcompiler_47.dll',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\d3dcompiler_47.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\libEGL.dll',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\libEGL.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\libGLESv2.dll',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\libGLESv2.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\libcrypto-1_1-x64.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\libcrypto-1_1-x64.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\libeay32.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\libeay32.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\libssl-1_1-x64.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\libssl-1_1-x64.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\msvcp140.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\msvcp140.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\msvcp140_1.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\msvcp140_1.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\msvcp140_2.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\msvcp140_2.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\opengl32sw.dll',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\opengl32sw.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\ssleay32.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\ssleay32.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\vcruntime140.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\vcruntime140.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\bin\\vcruntime140_1.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\vcruntime140_1.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\assetimporters\\assimp.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\assetimporters\\assimp.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\assetimporters\\uip.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\assetimporters\\uip.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\audio\\qtaudio_wasapi.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\audio\\qtaudio_wasapi.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\audio\\qtaudio_windows.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\audio\\qtaudio_windows.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\bearer\\qgenericbearer.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\bearer\\qgenericbearer.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\generic\\qtuiotouchplugin.dll',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\generic\\qtuiotouchplugin.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\geometryloaders\\defaultgeometryloader.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\geometryloaders\\defaultgeometryloader.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\geometryloaders\\gltfgeometryloader.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\geometryloaders\\gltfgeometryloader.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\geoservices\\qtgeoservices_esri.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\geoservices\\qtgeoservices_esri.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\geoservices\\qtgeoservices_itemsoverlay.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\geoservices\\qtgeoservices_itemsoverlay.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\geoservices\\qtgeoservices_mapbox.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\geoservices\\qtgeoservices_mapbox.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\geoservices\\qtgeoservices_nokia.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\geoservices\\qtgeoservices_nokia.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\geoservices\\qtgeoservices_osm.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\geoservices\\qtgeoservices_osm.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\iconengines\\qsvgicon.dll',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\iconengines\\qsvgicon.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qgif.dll',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qgif.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qicns.dll',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qicns.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qico.dll',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qico.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qjpeg.dll',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qjpeg.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qsvg.dll',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qsvg.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qtga.dll',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qtga.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qtiff.dll',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qtiff.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qwbmp.dll',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qwbmp.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\imageformats\\qwebp.dll',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\imageformats\\qwebp.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\mediaservice\\dsengine.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\mediaservice\\dsengine.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\mediaservice\\qtmedia_audioengine.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\mediaservice\\qtmedia_audioengine.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\mediaservice\\wmfengine.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\mediaservice\\wmfengine.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\platforms\\qminimal.dll',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\platforms\\qminimal.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\platforms\\qoffscreen.dll',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\platforms\\qoffscreen.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\platforms\\qwebgl.dll',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\platforms\\qwebgl.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\platforms\\qwindows.dll',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\platforms\\qwindows.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\platformthemes\\qxdgdesktopportal.dll',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\platformthemes\\qxdgdesktopportal.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\playlistformats\\qtmultimedia_m3u.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\playlistformats\\qtmultimedia_m3u.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\position\\qtposition_positionpoll.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\position\\qtposition_positionpoll.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\position\\qtposition_serialnmea.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\position\\qtposition_serialnmea.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\position\\qtposition_winrt.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\position\\qtposition_winrt.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\printsupport\\windowsprintersupport.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\printsupport\\windowsprintersupport.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\renderers\\openglrenderer.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\renderers\\openglrenderer.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\sceneparsers\\gltfsceneexport.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\sceneparsers\\gltfsceneexport.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\sceneparsers\\gltfsceneimport.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\sceneparsers\\gltfsceneimport.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\sensorgestures\\qtsensorgestures_plugin.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\sensorgestures\\qtsensorgestures_plugin.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\sensorgestures\\qtsensorgestures_shakeplugin.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\sensorgestures\\qtsensorgestures_shakeplugin.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\sensors\\qtsensors_generic.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\sensors\\qtsensors_generic.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\sqldrivers\\qsqlite.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\sqldrivers\\qsqlite.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\sqldrivers\\qsqlodbc.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\sqldrivers\\qsqlodbc.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\sqldrivers\\qsqlpsql.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\sqldrivers\\qsqlpsql.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\styles\\qwindowsvistastyle.dll',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\styles\\qwindowsvistastyle.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\texttospeech\\qtexttospeech_sapi.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\texttospeech\\qtexttospeech_sapi.dll',
   'BINARY'),
  ('PyQt5\\Qt5\\plugins\\webview\\qtwebview_webengine.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\plugins\\webview\\qtwebview_webengine.dll',
   'BINARY'),
  ('ffmpeg\\ffmpeg.exe',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\ffmpeg\\ffmpeg.exe',
   'BINARY'),
  ('ffmpeg\\ffprobe.exe',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\ffmpeg\\ffprobe.exe',
   'BINARY'),
  ('python311.dll',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\python311.dll',
   'BINARY'),
  ('_decimal.pyd',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\DLLs\\_decimal.pyd',
   'EXTENSION'),
  ('_hashlib.pyd',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\DLLs\\_hashlib.pyd',
   'EXTENSION'),
  ('_lzma.pyd',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\DLLs\\_lzma.pyd',
   'EXTENSION'),
  ('_bz2.pyd',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\DLLs\\_bz2.pyd',
   'EXTENSION'),
  ('unicodedata.pyd',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\DLLs\\unicodedata.pyd',
   'EXTENSION'),
  ('select.pyd',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\DLLs\\select.pyd',
   'EXTENSION'),
  ('_socket.pyd',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\DLLs\\_socket.pyd',
   'EXTENSION'),
  ('_multiprocessing.pyd',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\DLLs\\_multiprocessing.pyd',
   'EXTENSION'),
  ('pyexpat.pyd',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\DLLs\\pyexpat.pyd',
   'EXTENSION'),
  ('_ssl.pyd',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\DLLs\\_ssl.pyd',
   'EXTENSION'),
  ('_ctypes.pyd',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\DLLs\\_ctypes.pyd',
   'EXTENSION'),
  ('_queue.pyd',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\DLLs\\_queue.pyd',
   'EXTENSION'),
  ('psutil\\_psutil_windows.pyd',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\site-packages\\psutil\\_psutil_windows.pyd',
   'EXTENSION'),
  ('PyQt5\\QtGui.pyd',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\site-packages\\PyQt5\\QtGui.pyd',
   'EXTENSION'),
  ('PyQt5\\sip.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\site-packages\\PyQt5\\sip.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('PyQt5\\QtCore.pyd',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\site-packages\\PyQt5\\QtCore.pyd',
   'EXTENSION'),
  ('PyQt5\\QtWidgets.pyd',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\site-packages\\PyQt5\\QtWidgets.pyd',
   'EXTENSION'),
  ('VCRUNTIME140.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\VCRUNTIME140.dll',
   'BINARY'),
  ('VCRUNTIME140_1.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\VCRUNTIME140_1.dll',
   'BINARY'),
  ('Qt5Core.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Core.dll',
   'BINARY'),
  ('api-ms-win-crt-runtime-l1-1-0.dll',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\api-ms-win-crt-runtime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-heap-l1-1-0.dll',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\api-ms-win-crt-heap-l1-1-0.dll',
   'BINARY'),
  ('MSVCP140.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\MSVCP140.dll',
   'BINARY'),
  ('api-ms-win-crt-utility-l1-1-0.dll',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\api-ms-win-crt-utility-l1-1-0.dll',
   'BINARY'),
  ('MSVCP140_1.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\MSVCP140_1.dll',
   'BINARY'),
  ('api-ms-win-crt-stdio-l1-1-0.dll',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\api-ms-win-crt-stdio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-environment-l1-1-0.dll',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\api-ms-win-crt-environment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-filesystem-l1-1-0.dll',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\api-ms-win-crt-filesystem-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-time-l1-1-0.dll',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\api-ms-win-crt-time-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-math-l1-1-0.dll',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\api-ms-win-crt-math-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-string-l1-1-0.dll',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\api-ms-win-crt-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-convert-l1-1-0.dll',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\api-ms-win-crt-convert-l1-1-0.dll',
   'BINARY'),
  ('Qt5Xml.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Xml.dll',
   'BINARY'),
  ('Qt5Gui.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Gui.dll',
   'BINARY'),
  ('Qt5Widgets.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Widgets.dll',
   'BINARY'),
  ('api-ms-win-crt-locale-l1-1-0.dll',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\api-ms-win-crt-locale-l1-1-0.dll',
   'BINARY'),
  ('Qt5Sql.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Sql.dll',
   'BINARY'),
  ('Qt5QmlModels.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5QmlModels.dll',
   'BINARY'),
  ('Qt5Positioning.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Positioning.dll',
   'BINARY'),
  ('Qt5PositioningQuick.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5PositioningQuick.dll',
   'BINARY'),
  ('Qt5Quick.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Quick.dll',
   'BINARY'),
  ('Qt5Qml.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Qml.dll',
   'BINARY'),
  ('Qt5Network.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Network.dll',
   'BINARY'),
  ('Qt5Multimedia.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Multimedia.dll',
   'BINARY'),
  ('Qt5Quick3DUtils.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Quick3DUtils.dll',
   'BINARY'),
  ('Qt5Quick3DRuntimeRender.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Quick3DRuntimeRender.dll',
   'BINARY'),
  ('Qt5Quick3DRender.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Quick3DRender.dll',
   'BINARY'),
  ('Qt5Quick3DAssetImport.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Quick3DAssetImport.dll',
   'BINARY'),
  ('Qt5QuickTemplates2.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5QuickTemplates2.dll',
   'BINARY'),
  ('Qt5Test.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\Qt5Test.dll',
   'BINARY'),
  ('api-ms-win-crt-conio-l1-1-0.dll',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\api-ms-win-crt-conio-l1-1-0.dll',
   'BINARY'),
  ('libcrypto-1_1-x64.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\libcrypto-1_1-x64.dll',
   'BINARY'),
  ('LIBEAY32.dll',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\.venv\\Lib\\site-packages\\PyQt5\\Qt5\\bin\\LIBEAY32.dll',
   'BINARY'),
  ('LIBPQ.dll',
   'C:\\Users\\<USER>\\miniconda3\\Library\\bin\\LIBPQ.dll',
   'BINARY'),
  ('api-ms-win-crt-multibyte-l1-1-0.dll',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\api-ms-win-crt-multibyte-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-2-0.dll',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\api-ms-win-core-synch-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-crt-private-l1-1-0.dll',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\api-ms-win-crt-private-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-process-l1-1-0.dll',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\api-ms-win-crt-process-l1-1-0.dll',
   'BINARY'),
  ('zlib.dll', 'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\zlib.dll', 'BINARY'),
  ('libcrypto-3-x64.dll',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Library\\bin\\libcrypto-3-x64.dll',
   'BINARY'),
  ('liblzma.dll',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Library\\bin\\liblzma.dll',
   'BINARY'),
  ('LIBBZ2.dll',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Library\\bin\\LIBBZ2.dll',
   'BINARY'),
  ('libssl-3-x64.dll',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Library\\bin\\libssl-3-x64.dll',
   'BINARY'),
  ('ffi.dll',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Library\\bin\\ffi.dll',
   'BINARY'),
  ('python3.dll',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\python3.dll',
   'BINARY'),
  ('ucrtbase.dll',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\ucrtbase.dll',
   'BINARY'),
  ('gssapi64.dll',
   'C:\\Users\\<USER>\\miniconda3\\Library\\bin\\gssapi64.dll',
   'BINARY'),
  ('api-ms-win-core-rtlsupport-l1-1-0.dll',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\api-ms-win-core-rtlsupport-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-console-l1-1-0.dll',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\api-ms-win-core-console-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-profile-l1-1-0.dll',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\api-ms-win-core-profile-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-1.dll',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\api-ms-win-core-processthreads-l1-1-1.dll',
   'BINARY'),
  ('api-ms-win-core-localization-l1-2-0.dll',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\api-ms-win-core-localization-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l2-1-0.dll',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\api-ms-win-core-file-l2-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processenvironment-l1-1-0.dll',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\api-ms-win-core-processenvironment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-debug-l1-1-0.dll',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\api-ms-win-core-debug-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-namedpipe-l1-1-0.dll',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\api-ms-win-core-namedpipe-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-libraryloader-l1-1-0.dll',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\api-ms-win-core-libraryloader-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-util-l1-1-0.dll',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\api-ms-win-core-util-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-timezone-l1-1-0.dll',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\api-ms-win-core-timezone-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-heap-l1-1-0.dll',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\api-ms-win-core-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-2-0.dll',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\api-ms-win-core-file-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-string-l1-1-0.dll',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\api-ms-win-core-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-handle-l1-1-0.dll',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\api-ms-win-core-handle-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-datetime-l1-1-0.dll',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\api-ms-win-core-datetime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-1-0.dll',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\api-ms-win-core-synch-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-0.dll',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\api-ms-win-core-processthreads-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-interlocked-l1-1-0.dll',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\api-ms-win-core-interlocked-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-errorhandling-l1-1-0.dll',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\api-ms-win-core-errorhandling-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-fibers-l1-1-0.dll',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\api-ms-win-core-fibers-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-memory-l1-1-0.dll',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\api-ms-win-core-memory-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-1-0.dll',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\api-ms-win-core-file-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-sysinfo-l1-1-0.dll',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\api-ms-win-core-sysinfo-l1-1-0.dll',
   'BINARY'),
  ('comerr64.dll',
   'C:\\Users\\<USER>\\miniconda3\\Library\\bin\\comerr64.dll',
   'BINARY'),
  ('krb5_64.dll',
   'C:\\Users\\<USER>\\miniconda3\\Library\\bin\\krb5_64.dll',
   'BINARY'),
  ('k5sprt64.dll',
   'C:\\Users\\<USER>\\miniconda3\\Library\\bin\\k5sprt64.dll',
   'BINARY')],
 [],
 [],
 [('PyQt5\\Qt5\\translations\\qt_help_sk.qm',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_sk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_pt.qm',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_pt.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_ko.qm',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_ko.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_lv.qm',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_lv.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_fa.qm',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_fa.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_sl.qm',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_sl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_sk.qm',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_sk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_hu.qm',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_hu.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_da.qm',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_da.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_fi.qm',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_fi.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_de.qm',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_de.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_gl.qm',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_gl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_es.qm',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_es.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_hu.qm',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_hu.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_da.qm',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_da.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_fi.qm',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_fi.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_it.qm',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_it.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_pl.qm',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_pl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_ru.qm',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_ru.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_uk.qm',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_uk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_sv.qm',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_sv.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_tr.qm',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_tr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_fr.qm',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_fr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_ja.qm',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_ja.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_he.qm',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_he.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_gl.qm',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_gl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_ru.qm',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_ru.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_ca.qm',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_ca.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_bg.qm',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_bg.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_ru.qm',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_ru.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_gd.qm',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_gd.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_zh_TW.qm',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_zh_TW.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_ar.qm',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_ar.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_de.qm',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_de.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_he.qm',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_he.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_ko.qm',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_ko.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_fr.qm',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_fr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_zh_CN.qm',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_zh_CN.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_en.qm',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_en.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_de.qm',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_de.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_ja.qm',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_ja.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_cs.qm',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_cs.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_fr.qm',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_fr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_es.qm',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_es.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_ca.qm',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_ca.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_tr.qm',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_tr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_es.qm',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_es.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_cs.qm',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_cs.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_uk.qm',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_uk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_gd.qm',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_gd.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_ar.qm',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_ar.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_en.qm',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_en.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_it.qm',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_it.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_sl.qm',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_sl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_bg.qm',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_bg.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_uk.qm',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_uk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_pl.qm',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_pl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_zh_CN.qm',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_zh_CN.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_it.qm',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_it.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_ja.qm',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_ja.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_en.qm',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_en.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_lt.qm',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_lt.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_pl.qm',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_pl.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_bg.qm',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_bg.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_cs.qm',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_cs.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_da.qm',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_da.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_tr.qm',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_tr.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_lv.qm',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_lv.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_ar.qm',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_ar.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_ca.qm',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_ca.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_sk.qm',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_sk.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_hu.qm',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_hu.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_zh_TW.qm',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_zh_TW.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qtbase_zh_TW.qm',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qtbase_zh_TW.qm',
   'DATA'),
  ('PyQt5\\Qt5\\translations\\qt_help_ko.qm',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\site-packages\\PyQt5\\Qt5\\translations\\qt_help_ko.qm',
   'DATA'),
  ('base_library.zip',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\build\\视频批量转换工具\\base_library.zip',
   'DATA')],
 [('posixpath',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\posixpath.py',
   'PYMODULE'),
  ('locale',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\locale.py',
   'PYMODULE'),
  ('_collections_abc',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\_collections_abc.py',
   'PYMODULE'),
  ('weakref',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\weakref.py',
   'PYMODULE'),
  ('io', 'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\io.py', 'PYMODULE'),
  ('traceback',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\traceback.py',
   'PYMODULE'),
  ('functools',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\functools.py',
   'PYMODULE'),
  ('stat', 'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\stat.py', 'PYMODULE'),
  ('os', 'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\os.py', 'PYMODULE'),
  ('enum', 'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\enum.py', 'PYMODULE'),
  ('codecs',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\codecs.py',
   'PYMODULE'),
  ('sre_parse',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\sre_parse.py',
   'PYMODULE'),
  ('types',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\types.py',
   'PYMODULE'),
  ('collections.abc',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\collections\\abc.py',
   'PYMODULE'),
  ('collections',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\collections\\__init__.py',
   'PYMODULE'),
  ('sre_constants',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\sre_constants.py',
   'PYMODULE'),
  ('copyreg',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\copyreg.py',
   'PYMODULE'),
  ('genericpath',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\genericpath.py',
   'PYMODULE'),
  ('heapq',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\heapq.py',
   'PYMODULE'),
  ('keyword',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\keyword.py',
   'PYMODULE'),
  ('_weakrefset',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\_weakrefset.py',
   'PYMODULE'),
  ('re._parser',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\re\\_parser.py',
   'PYMODULE'),
  ('re._constants',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\re\\_constants.py',
   'PYMODULE'),
  ('re._compiler',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\re\\_compiler.py',
   'PYMODULE'),
  ('re._casefix',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\re\\_casefix.py',
   'PYMODULE'),
  ('re',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\re\\__init__.py',
   'PYMODULE'),
  ('sre_compile',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\sre_compile.py',
   'PYMODULE'),
  ('linecache',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\linecache.py',
   'PYMODULE'),
  ('ntpath',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\ntpath.py',
   'PYMODULE'),
  ('reprlib',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\reprlib.py',
   'PYMODULE'),
  ('warnings',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\warnings.py',
   'PYMODULE'),
  ('abc', 'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\abc.py', 'PYMODULE'),
  ('operator',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\operator.py',
   'PYMODULE'),
  ('encodings.zlib_codec',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\encodings\\zlib_codec.py',
   'PYMODULE'),
  ('encodings.uu_codec',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\encodings\\uu_codec.py',
   'PYMODULE'),
  ('encodings.utf_8_sig',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\encodings\\utf_8_sig.py',
   'PYMODULE'),
  ('encodings.utf_8',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\encodings\\utf_8.py',
   'PYMODULE'),
  ('encodings.utf_7',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\encodings\\utf_7.py',
   'PYMODULE'),
  ('encodings.utf_32_le',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\encodings\\utf_32_le.py',
   'PYMODULE'),
  ('encodings.utf_32_be',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\encodings\\utf_32_be.py',
   'PYMODULE'),
  ('encodings.utf_32',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\encodings\\utf_32.py',
   'PYMODULE'),
  ('encodings.utf_16_le',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\encodings\\utf_16_le.py',
   'PYMODULE'),
  ('encodings.utf_16_be',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\encodings\\utf_16_be.py',
   'PYMODULE'),
  ('encodings.utf_16',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\encodings\\utf_16.py',
   'PYMODULE'),
  ('encodings.unicode_escape',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\encodings\\unicode_escape.py',
   'PYMODULE'),
  ('encodings.undefined',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\encodings\\undefined.py',
   'PYMODULE'),
  ('encodings.tis_620',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\encodings\\tis_620.py',
   'PYMODULE'),
  ('encodings.shift_jisx0213',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\encodings\\shift_jisx0213.py',
   'PYMODULE'),
  ('encodings.shift_jis_2004',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\encodings\\shift_jis_2004.py',
   'PYMODULE'),
  ('encodings.shift_jis',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\encodings\\shift_jis.py',
   'PYMODULE'),
  ('encodings.rot_13',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\encodings\\rot_13.py',
   'PYMODULE'),
  ('encodings.raw_unicode_escape',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\encodings\\raw_unicode_escape.py',
   'PYMODULE'),
  ('encodings.quopri_codec',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\encodings\\quopri_codec.py',
   'PYMODULE'),
  ('encodings.punycode',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\encodings\\punycode.py',
   'PYMODULE'),
  ('encodings.ptcp154',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\encodings\\ptcp154.py',
   'PYMODULE'),
  ('encodings.palmos',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\encodings\\palmos.py',
   'PYMODULE'),
  ('encodings.oem',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\encodings\\oem.py',
   'PYMODULE'),
  ('encodings.mbcs',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\encodings\\mbcs.py',
   'PYMODULE'),
  ('encodings.mac_turkish',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\encodings\\mac_turkish.py',
   'PYMODULE'),
  ('encodings.mac_romanian',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\encodings\\mac_romanian.py',
   'PYMODULE'),
  ('encodings.mac_roman',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\encodings\\mac_roman.py',
   'PYMODULE'),
  ('encodings.mac_latin2',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\encodings\\mac_latin2.py',
   'PYMODULE'),
  ('encodings.mac_iceland',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\encodings\\mac_iceland.py',
   'PYMODULE'),
  ('encodings.mac_greek',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\encodings\\mac_greek.py',
   'PYMODULE'),
  ('encodings.mac_farsi',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\encodings\\mac_farsi.py',
   'PYMODULE'),
  ('encodings.mac_cyrillic',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\encodings\\mac_cyrillic.py',
   'PYMODULE'),
  ('encodings.mac_croatian',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\encodings\\mac_croatian.py',
   'PYMODULE'),
  ('encodings.mac_arabic',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\encodings\\mac_arabic.py',
   'PYMODULE'),
  ('encodings.latin_1',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\encodings\\latin_1.py',
   'PYMODULE'),
  ('encodings.kz1048',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\encodings\\kz1048.py',
   'PYMODULE'),
  ('encodings.koi8_u',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\encodings\\koi8_u.py',
   'PYMODULE'),
  ('encodings.koi8_t',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\encodings\\koi8_t.py',
   'PYMODULE'),
  ('encodings.koi8_r',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\encodings\\koi8_r.py',
   'PYMODULE'),
  ('encodings.johab',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\encodings\\johab.py',
   'PYMODULE'),
  ('encodings.iso8859_9',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\encodings\\iso8859_9.py',
   'PYMODULE'),
  ('encodings.iso8859_8',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\encodings\\iso8859_8.py',
   'PYMODULE'),
  ('encodings.iso8859_7',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\encodings\\iso8859_7.py',
   'PYMODULE'),
  ('encodings.iso8859_6',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\encodings\\iso8859_6.py',
   'PYMODULE'),
  ('encodings.iso8859_5',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\encodings\\iso8859_5.py',
   'PYMODULE'),
  ('encodings.iso8859_4',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\encodings\\iso8859_4.py',
   'PYMODULE'),
  ('encodings.iso8859_3',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\encodings\\iso8859_3.py',
   'PYMODULE'),
  ('encodings.iso8859_2',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\encodings\\iso8859_2.py',
   'PYMODULE'),
  ('encodings.iso8859_16',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\encodings\\iso8859_16.py',
   'PYMODULE'),
  ('encodings.iso8859_15',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\encodings\\iso8859_15.py',
   'PYMODULE'),
  ('encodings.iso8859_14',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\encodings\\iso8859_14.py',
   'PYMODULE'),
  ('encodings.iso8859_13',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\encodings\\iso8859_13.py',
   'PYMODULE'),
  ('encodings.iso8859_11',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\encodings\\iso8859_11.py',
   'PYMODULE'),
  ('encodings.iso8859_10',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\encodings\\iso8859_10.py',
   'PYMODULE'),
  ('encodings.iso8859_1',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\encodings\\iso8859_1.py',
   'PYMODULE'),
  ('encodings.iso2022_kr',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\encodings\\iso2022_kr.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_ext',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\encodings\\iso2022_jp_ext.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_3',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\encodings\\iso2022_jp_3.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_2004',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\encodings\\iso2022_jp_2004.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_2',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\encodings\\iso2022_jp_2.py',
   'PYMODULE'),
  ('encodings.iso2022_jp_1',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\encodings\\iso2022_jp_1.py',
   'PYMODULE'),
  ('encodings.iso2022_jp',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\encodings\\iso2022_jp.py',
   'PYMODULE'),
  ('encodings.idna',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\encodings\\idna.py',
   'PYMODULE'),
  ('encodings.hz',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\encodings\\hz.py',
   'PYMODULE'),
  ('encodings.hp_roman8',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\encodings\\hp_roman8.py',
   'PYMODULE'),
  ('encodings.hex_codec',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\encodings\\hex_codec.py',
   'PYMODULE'),
  ('encodings.gbk',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\encodings\\gbk.py',
   'PYMODULE'),
  ('encodings.gb2312',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\encodings\\gb2312.py',
   'PYMODULE'),
  ('encodings.gb18030',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\encodings\\gb18030.py',
   'PYMODULE'),
  ('encodings.euc_kr',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\encodings\\euc_kr.py',
   'PYMODULE'),
  ('encodings.euc_jp',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\encodings\\euc_jp.py',
   'PYMODULE'),
  ('encodings.euc_jisx0213',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\encodings\\euc_jisx0213.py',
   'PYMODULE'),
  ('encodings.euc_jis_2004',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\encodings\\euc_jis_2004.py',
   'PYMODULE'),
  ('encodings.cp950',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\encodings\\cp950.py',
   'PYMODULE'),
  ('encodings.cp949',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\encodings\\cp949.py',
   'PYMODULE'),
  ('encodings.cp932',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\encodings\\cp932.py',
   'PYMODULE'),
  ('encodings.cp875',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\encodings\\cp875.py',
   'PYMODULE'),
  ('encodings.cp874',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\encodings\\cp874.py',
   'PYMODULE'),
  ('encodings.cp869',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\encodings\\cp869.py',
   'PYMODULE'),
  ('encodings.cp866',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\encodings\\cp866.py',
   'PYMODULE'),
  ('encodings.cp865',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\encodings\\cp865.py',
   'PYMODULE'),
  ('encodings.cp864',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\encodings\\cp864.py',
   'PYMODULE'),
  ('encodings.cp863',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\encodings\\cp863.py',
   'PYMODULE'),
  ('encodings.cp862',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\encodings\\cp862.py',
   'PYMODULE'),
  ('encodings.cp861',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\encodings\\cp861.py',
   'PYMODULE'),
  ('encodings.cp860',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\encodings\\cp860.py',
   'PYMODULE'),
  ('encodings.cp858',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\encodings\\cp858.py',
   'PYMODULE'),
  ('encodings.cp857',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\encodings\\cp857.py',
   'PYMODULE'),
  ('encodings.cp856',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\encodings\\cp856.py',
   'PYMODULE'),
  ('encodings.cp855',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\encodings\\cp855.py',
   'PYMODULE'),
  ('encodings.cp852',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\encodings\\cp852.py',
   'PYMODULE'),
  ('encodings.cp850',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\encodings\\cp850.py',
   'PYMODULE'),
  ('encodings.cp775',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\encodings\\cp775.py',
   'PYMODULE'),
  ('encodings.cp737',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\encodings\\cp737.py',
   'PYMODULE'),
  ('encodings.cp720',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\encodings\\cp720.py',
   'PYMODULE'),
  ('encodings.cp500',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\encodings\\cp500.py',
   'PYMODULE'),
  ('encodings.cp437',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\encodings\\cp437.py',
   'PYMODULE'),
  ('encodings.cp424',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\encodings\\cp424.py',
   'PYMODULE'),
  ('encodings.cp273',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\encodings\\cp273.py',
   'PYMODULE'),
  ('encodings.cp1258',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\encodings\\cp1258.py',
   'PYMODULE'),
  ('encodings.cp1257',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\encodings\\cp1257.py',
   'PYMODULE'),
  ('encodings.cp1256',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\encodings\\cp1256.py',
   'PYMODULE'),
  ('encodings.cp1255',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\encodings\\cp1255.py',
   'PYMODULE'),
  ('encodings.cp1254',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\encodings\\cp1254.py',
   'PYMODULE'),
  ('encodings.cp1253',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\encodings\\cp1253.py',
   'PYMODULE'),
  ('encodings.cp1252',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\encodings\\cp1252.py',
   'PYMODULE'),
  ('encodings.cp1251',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\encodings\\cp1251.py',
   'PYMODULE'),
  ('encodings.cp1250',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\encodings\\cp1250.py',
   'PYMODULE'),
  ('encodings.cp1140',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\encodings\\cp1140.py',
   'PYMODULE'),
  ('encodings.cp1125',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\encodings\\cp1125.py',
   'PYMODULE'),
  ('encodings.cp1026',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\encodings\\cp1026.py',
   'PYMODULE'),
  ('encodings.cp1006',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\encodings\\cp1006.py',
   'PYMODULE'),
  ('encodings.cp037',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\encodings\\cp037.py',
   'PYMODULE'),
  ('encodings.charmap',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\encodings\\charmap.py',
   'PYMODULE'),
  ('encodings.bz2_codec',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\encodings\\bz2_codec.py',
   'PYMODULE'),
  ('encodings.big5hkscs',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\encodings\\big5hkscs.py',
   'PYMODULE'),
  ('encodings.big5',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\encodings\\big5.py',
   'PYMODULE'),
  ('encodings.base64_codec',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\encodings\\base64_codec.py',
   'PYMODULE'),
  ('encodings.ascii',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\encodings\\ascii.py',
   'PYMODULE'),
  ('encodings.aliases',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\encodings\\aliases.py',
   'PYMODULE'),
  ('encodings',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\encodings\\__init__.py',
   'PYMODULE')])
