# 视频批量转换工具使用说明

## 一、软件说明
这是一个智能视频批量转换工具，专注于在保持视频质量的同时实现格式转换和压缩。本工具已包含所有必要的依赖（包括ffmpeg），可以直接运行而无需额外安装。具有以下特点：
- 智能码率计算：根据原视频参数自动计算最优码率，避免画质损失
- 动态质量控制：自动设置最小和最大码率范围，确保稳定的输出质量
- 格式优化策略：针对不同格式（mp4、mkv、avi）采用不同的编码和压缩策略
- 并行处理：支持多核心处理和GPU加速，显著提升转换效率
- 灵活配置：可选择保持原视频质量或自定义压缩参数

## 二、目录结构
```
dist/
  ├── 视频批量转换工具.exe  # 主程序
  ├── config.json          # 配置文件
  ├── input/              # 输入目录（存放待转换的视频）
  └── output/             # 输出目录（存放转换后的视频）
```

## 三、使用步骤
1. 准备工作：
   - 将需要转换的视频文件放入input目录
   - 确保output目录有足够的存储空间

2. 启动程序：
   - 双击运行"视频批量转换工具.exe"
   - 程序会自动检测系统环境（CPU核心数、GPU支持等）

3. 配置转换参数：
   - 基本设置：
     * 选择输出格式（mp4/mkv/avi）
     * 设置分辨率或保持原分辨率
   - 质量设置：
     * 选择是否使用智能码率计算
     * 可手动设置视频和音频比特率
   - 性能设置：
     * 选择是否启用GPU加速
     * 设置最大重试次数

4. 开始转换：
   - 点击"开始转换"按钮
   - 实时监控转换进度和日志
   - 可随时暂停/继续转换过程

5. 完成转换：
   - 在output目录查看转换后的视频
   - 检查转换日志了解详细信息
   - 对比原视频确认质量符合预期

## 四、功能特点

### 1. 智能视频质量处理
- 智能码率计算：根据原视频参数自动计算最优码率
- 格式优化：
  * MP4格式：使用H.264编码，平衡压缩率和兼容性
  * MKV格式：采用高效的HEVC/H.265编码
  * AVI格式：使用传统编码，确保最大兼容性
- 质量保护：自动设置最小和最大码率范围，避免过度压缩
- 分辨率智能适配：可保持原分辨率或按比例缩放

### 2. 高效处理机制
- 多核心并行处理：自动利用CPU多核心进行转换
- GPU加速支持：支持NVIDIA显卡加速，大幅提升转换速度
- 智能任务调度：根据系统资源自动调整并行任务数
- 断点续传：支持暂停/继续转换过程
- 自动重试：转换失败时智能重试

### 3. 便捷功能
- 批量处理：同时处理多个视频文件
- 进度监控：实时显示总体进度和当前文件进度
- 详细日志：记录转换参数和过程信息
- 状态保存：记住上次使用的目录和设置

## 五、参数说明

### 1. 基本设置
- 输入目录：存放待转换视频的文件夹
- 输出目录：存放转换后视频的文件夹
- 记住目录选择：下次启动时自动填充上次使用的目录

### 2. 转换设置
- 保持原分辨率：
  * 启用时：完全保持原视频质量，仅转换格式
  * 关闭时：可自定义分辨率和码率
- 输出格式选择：
  * MP4：最佳兼容性，适合多设备播放
  * MKV：更高压缩率，适合存储大文件
  * AVI：传统格式，适合老设备播放
- 分辨率设置：
  * 1920x1080：全高清，适合高质量视频
  * 1280x720：平衡质量和文件大小
  * 854x480：适合移动设备播放
- 视频比特率：
  * 自动模式：根据分辨率智能计算
  * 手动设置：可根据需求自定义（如2000k）
- 音频比特率：
  * 默认128k：适合大多数场景
  * 可根据音质需求调整

### 3. 高级设置
- 覆盖已存在文件：是否覆盖同名文件
- 使用GPU加速：
  * 启用时：利用显卡加速，大幅提升速度
  * 关闭时：使用CPU编码，兼容性更好
- 最大重试次数：转换失败时的自动重试次数
- 缓冲区大小：自动根据码率优化

## 六、注意事项

### 1. 运行环境
- 本程序为绿色版，无需安装任何额外软件
- 已内置ffmpeg视频处理引擎，无需单独安装
- 支持Windows系统（Windows 7及以上版本）
- 首次运行可能需要管理员权限

### 2. 视频质量相关
- 选择合适的输出格式：
  * 追求画质：使用MKV格式，保持原分辨率
  * 追求兼容：使用MP4格式，适当降低码率
  * 节省空间：可适当降低分辨率和码率
- 使用GPU加速时：
  * 确保显卡支持NVENC编码
  * 关闭其他占用GPU的程序
  * 保持显卡驱动为最新版本

### 2. 系统资源相关
- 确保足够的磁盘空间：
  * 输入目录：存放原始视频
  * 输出目录：预留至少与原视频相同的空间
  * 临时文件：建议预留10GB以上空间
- CPU和内存使用：
  * 程序会自动调整并行任务数
  * 可以在任务管理器监控资源使用

### 3. 使用建议
- 首次使用时先测试单个视频
- 定期查看转换日志了解处理状态
- 对于重要视频建议备份原文件
- 如遇问题可查看界面下方的日志信息

## 七、常见问题
1. 程序无法启动
   - 检查是否以管理员身份运行
   - 检查杀毒软件是否拦截

2. 转换失败
   - 检查视频文件是否完整
   - 检查磁盘空间是否充足
   - 查看日志了解具体错误原因

3. GPU加速不可用
   - 检查显卡是否支持NVENC编码
   - 更新显卡驱动到最新版本

4. 转换速度慢
   - 考虑开启GPU加速
   - 降低输出视频的分辨率和码率
   - 关闭其他占用系统资源的程序
