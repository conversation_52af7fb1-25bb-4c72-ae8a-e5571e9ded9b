import os
import subprocess
import re
import shutil
import sys
import logging
import argparse
from pathlib import Path
import concurrent.futures
import psutil
from threading import Lock, Event, Semaphore
import json
from datetime import datetime
import time
from video_utils import get_video_info, build_ffmpeg_command
import locale
import signal
import atexit

# 用于存储所有ffmpeg进程的列表
ffmpeg_processes = []
ffmpeg_processes_lock = Lock()

# 最大同时运行的ffmpeg进程数
MAX_FFMPEG_PROCESSES = max(1, psutil.cpu_count(logical=False) - 1)
ffmpeg_semaphore = Semaphore(MAX_FFMPEG_PROCESSES)

def cleanup_processes():
    """清理所有ffmpeg进程"""
    with ffmpeg_processes_lock:
        for proc in ffmpeg_processes[:]:  # 使用切片创建副本以避免在迭代时修改列表
            try:
                if proc.is_running():
                    proc.terminate()
                ffmpeg_processes.remove(proc)
            except (psutil.NoSuchProcess, psutil.AccessDenied, ValueError):
                pass

def signal_handler(signum, frame):
    """处理中断信号"""
    cleanup_processes()

# 注册清理函数和信号处理
atexit.register(cleanup_processes)
signal.signal(signal.SIGINT, signal_handler)
signal.signal(signal.SIGTERM, signal_handler)

def set_terminal_encoding():
    """根据操作系统设置终端编码为 UTF-8，避免输出乱码。"""
    if sys.platform == "win32":
        try:
            import ctypes
            ctypes.windll.kernel32.SetConsoleOutputCP(65001)
        except Exception as e:
            print(f"无法设置 Windows 终端编码为 UTF-8: {e}", file=sys.stderr)
    elif sys.platform in ("linux", "darwin"):
        try:
            locale.setlocale(locale.LC_ALL, '')
            sys.stdout.reconfigure(encoding='utf-8')
        except Exception as e:
            print(f"无法设置 {sys.platform.capitalize()} 终端编码为 UTF-8: {e}", file=sys.stderr)

class SafeStreamHandler(logging.StreamHandler):
    """确保日志消息在特殊字符转义后被正确写入终端。"""
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.buffer = []
        self.buffer_size = 1000  # 最多保留1000条日志

    def emit(self, record):
        try:
            msg = self.format(record)
            if isinstance(msg, str):
                msg = msg.encode('utf-8', errors='replace').decode('utf-8')
            
            # 添加到缓冲区
            self.buffer.append(msg)
            if len(self.buffer) > self.buffer_size:
                self.buffer.pop(0)  # 移除最旧的日志
            
            self.stream.write(msg + self.terminator)
            self.flush()
        except Exception:
            self.handleError(record)

def setup_logging():
    """配置日志记录，设置日志级别和格式，并添加流处理器。"""
    logger = logging.getLogger()
    logger.setLevel(logging.INFO)
    formatter = logging.Formatter("%(asctime)s [%(levelname)s] %(message)s")
    stream_handler = SafeStreamHandler(sys.stdout)
    stream_handler.setFormatter(formatter)
    logger.addHandler(stream_handler)

def check_dependencies():
    """检查必要的命令行工具（ffmpeg 和 ffprobe）是否可用。"""
    from ffmpeg_utils import get_ffmpeg_path, get_ffprobe_path
    import os

    missing_deps = []
    ffmpeg_path = get_ffmpeg_path()
    ffprobe_path = get_ffprobe_path()

    if not os.path.exists(ffmpeg_path):
        missing_deps.append('ffmpeg')
    if not os.path.exists(ffprobe_path):
        missing_deps.append('ffprobe')

    if missing_deps:
        error_msg = f"以下命令未找到: {', '.join(missing_deps)}。程序无法继续运行。"
        logging.error(error_msg)
        raise RuntimeError(error_msg)

def parse_arguments():
    """解析命令行参数，提供用户可配置的选项。"""
    parser = argparse.ArgumentParser(description="批量转码视频文件")
    parser.add_argument('--input_dir', type=str, default='input', help='输入文件夹路径')
    parser.add_argument('--output_dir', type=str, default='output', help='输出文件夹路径')
    parser.add_argument('--video_format', type=str, help='输出视频格式')
    parser.add_argument('--resolution', type=str, help='视频分辨率')
    parser.add_argument('--video_bitrate', type=str, help='视频比特率')
    parser.add_argument('--audio_bitrate', type=str, help='音频比特率')
    parser.add_argument('--overwrite', action='store_true', help='是否覆盖已存在的文件')
    parser.add_argument('--min_size', type=float, default=0, help='最小文件大小（MB）')
    parser.add_argument('--max_size', type=float, default=float('inf'), help='最大文件大小（MB）')
    parser.add_argument('--output_name', type=str, help='输出文件名模板')
    parser.add_argument('--priority', type=int, default=psutil.NORMAL_PRIORITY_CLASS, help='进程优先级')
    parser.add_argument('--use_gpu', action='store_true', help='是否使用GPU加速')
    parser.add_argument('--max_retries', type=int, default=3, help='转码失败时的最大重试次数')
    parser.add_argument('--keep_original_resolution', action='store_true', help='保持原视频分辨率')
    parser.add_argument('--auto_detect_videos', action='store_true', help='自动检测所有视频文件')
    return parser.parse_args()

def validate_arguments(args, config):
    """验证命令行参数和配置文件中的参数格式是否正确。"""
    if args.resolution and not re.match(r'\d+x\d+', args.resolution):
        raise ValueError("分辨率格式不正确，应为 'WIDTHxHEIGHT'")
    
    if args.video_bitrate and not re.match(r'\d+k', args.video_bitrate):
        raise ValueError("视频比特率格式不正确，应为 'NUMBERk'")
    
    if args.audio_bitrate and not re.match(r'\d+k', args.audio_bitrate):
        raise ValueError("音频比特率格式不正确，应为 'NUMBERk'")

def load_config():
    """加载配置文件，如果文件不存在则返回默认配置。"""
    config_path = Path('config.json')
    if config_path.exists():
        with open(config_path, 'r') as f:
            config = json.load(f)
    else:
        config = {
            "input_dir": "input",
            "output_dir": "output",
            "video_format": "mp4",
            "resolution": "1280x720",
            "video_bitrate": "2000k",
            "audio_bitrate": "128k",
            "overwrite": True,
            "use_gpu": False,
            "max_retries": 1,
            "supported_formats": [".wmv", ".avi", ".mp4", ".mkv"],
            "output_name": "{name}_{resolution}_{timestamp}",
            "min_size": 0,
            "max_size": 10240,
            "keep_original_resolution": False,
            "auto_detect_videos": False
        }
    return config

def save_config(config):
    """保存配置文件。"""
    with open('config.json', 'w') as f:
        json.dump(config, f, indent=4)

def format_video_info(video_info):
    """格式化视频信息为易读的字符串。"""
    if not video_info:
        return "无法获取视频信息"
    
    resolution = f"{video_info.get('width', 'N/A')}x{video_info.get('height', 'N/A')}"
    bitrate = f"{int(video_info.get('bitrate', 0)) // 1000}k" if video_info.get('bitrate') else "N/A"
    
    return f"分辨率: {resolution}, 码率: {bitrate}"

def process_single_video(video_file, config, output_path, progress_callback=None, pause_event=None):
    """处理单个视频文件，包括获取视频信息、构建 FFmpeg 命令、执行转码并更新进度。"""
    while pause_event and pause_event.is_set():
        time.sleep(0.1)  # 暂停时等待继续信号
        
    video_info = get_video_info(video_file)
    if not video_info:
        logging.error(f"无法获取视频 '{video_file.name}' 的信息，跳过处理。")
        return False

    logging.info(f"原始视频 '{video_file.name}' 信息：{format_video_info(video_info)}")

    output_format = config['video_format'].lower()
    if not output_format.startswith('.'):
        output_format = '.' + output_format

    # 处理保持原分辨率的情况
    if config.get('keep_original_resolution', False):
        config['resolution'] = f"{video_info['width']}x{video_info['height']}"
        config['video_bitrate'] = f"{int(video_info['bitrate']) // 1000}k" if video_info['bitrate'] else config['video_bitrate']
        logging.info(f"保持原视频质量：分辨率 {config['resolution']}，码率 {config['video_bitrate']}")

    output_name = config['output_name'].format(
        name=video_file.stem,
        resolution=config['resolution'],
        timestamp=datetime.now().strftime("%Y%m%d_%H%M%S")
    )
    output_file = output_path / f"{output_name}{output_format}"

    if output_file.exists() and not config['overwrite']:
        logging.info(f"输出文件 '{output_file}' 已存在，跳过。")
        return False

    command = build_ffmpeg_command(video_file, config, output_file, video_info)

    for attempt in range(config['max_retries']):
        try:
            # 使用信号量限制同时运行的ffmpeg进程数量
            with ffmpeg_semaphore:
                with subprocess.Popen(
                    command,
                    creationflags=subprocess.CREATE_NO_WINDOW,
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    text=True,
                    encoding='utf-8',
                    errors='replace'
                ) as process:
                    psutil.Process(process.pid).nice(config['priority'])
                    
                    time_pattern = re.compile(r'time=(\d{1,2}):(\d{2}):(\d{2}(?:\.\d+)?)')
                    
                    ffmpeg_process = psutil.Process(process.pid)
                    with ffmpeg_processes_lock:
                        ffmpeg_processes.append(ffmpeg_process)
                    
                    try:
                        for line in process.stderr:
                            if pause_event and pause_event.is_set():
                                # 暂停所有ffmpeg进程
                                with ffmpeg_processes_lock:
                                    for proc in ffmpeg_processes:
                                        try:
                                            if proc.is_running():
                                                proc.suspend()
                                        except (psutil.NoSuchProcess, psutil.AccessDenied):
                                            pass
                                
                                # 等待继续信号
                                while pause_event.is_set():
                                    time.sleep(0.1)
                                
                                # 恢复所有ffmpeg进程
                                with ffmpeg_processes_lock:
                                    for proc in ffmpeg_processes:
                                        try:
                                            if proc.is_running():
                                                proc.resume()
                                        except (psutil.NoSuchProcess, psutil.AccessDenied):
                                            pass
                                
                            match = time_pattern.search(line)
                            if match:
                                hours, minutes, seconds = match.groups()
                                current_time = float(hours) * 3600 + float(minutes) * 60 + float(seconds)
                                if progress_callback and video_info['duration']:
                                    file_progress = min(current_time / video_info['duration'], 1.0)
                                    progress_callback(file_progress)

                        if process.wait() != 0:
                            raise subprocess.CalledProcessError(process.returncode, command)
                    finally:
                        # 确保进程从列表中移除
                        with ffmpeg_processes_lock:
                            try:
                                if ffmpeg_process in ffmpeg_processes:
                                    ffmpeg_processes.remove(ffmpeg_process)
                            except ValueError:
                                pass

            logging.info(f"转码 '{video_file.name}' 成功。")
            
            # 获取并显示转换后的视频信息
            output_video_info = get_video_info(output_file)
            if output_video_info:
                logging.info(f"转换后视频 '{output_file.name}' 信息：{format_video_info(output_video_info)}")
                
                # 计算并显示码率变化
                original_bitrate = int(video_info.get('bitrate', 0)) // 1000
                new_bitrate = int(output_video_info.get('bitrate', 0)) // 1000
                bitrate_change = new_bitrate - original_bitrate
                bitrate_change_percent = (bitrate_change / original_bitrate) * 100 if original_bitrate > 0 else 0
                
                logging.info(f"码率变化: {bitrate_change:+d}k ({bitrate_change_percent:.2f}%)")
            else:
                logging.warning(f"无法获取转换后视频 '{output_file.name}' 的信息。")
            
            return True

        except subprocess.CalledProcessError as e:
            logging.error(f"转码 '{video_file.name}' 失败（尝试 {attempt + 1}/{config['max_retries']}）。错误输出:\n{e.stderr}")
            if attempt == config['max_retries'] - 1:
                return False

        except Exception as e:
            logging.error(f"转码 '{video_file.name}' 时发生错误（尝试 {attempt + 1}/{config['max_retries']}）: {e}")
            if attempt == config['max_retries'] - 1:
                return False

def filter_videos(video_files, min_size, max_size):
    """过滤视频文件，根据最小和最大文件大小。"""
    return [
        f for f in video_files
        if min_size <= f.stat().st_size / (1024 * 1024) <= max_size
    ]

def transcode_videos_with_detailed_progress(config, progress_callback=None, pause_event=None):
    """批量转码视频文件，并通过回调函数更新进度。"""
    if pause_event and pause_event.is_set():
        return

    input_path = Path(config['input_dir'])
    if not input_path.exists() or not input_path.is_dir():
        logging.error(f"输入文件夹 '{config['input_dir']}' 不存在或不是一个文件夹。")
        return

    output_path = Path(config['output_dir'])
    output_path.mkdir(parents=True, exist_ok=True)

    # 确保所有格式都是小写并且以点开头
    supported_formats = [fmt.lower() if fmt.startswith('.') else f'.{fmt.lower()}' for fmt in config['supported_formats']]
    
    # 使用ffprobe检测文件是否为有效视频文件
    def is_valid_video(file_path):
        from ffmpeg_utils import get_ffprobe_path
        try:
            cmd = [
                get_ffprobe_path(),
                '-v', 'error',
                '-select_streams', 'v:0',  # 选择第一个视频流
                '-show_entries', 'stream=codec_type',
                '-of', 'json',
                str(file_path)
            ]
            result = subprocess.run(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True, creationflags=subprocess.CREATE_NO_WINDOW)
            
            if result.returncode != 0:
                return False
                
            import json
            info = json.loads(result.stdout)
            
            # 检查是否有视频流
            if 'streams' in info and info['streams'] and info['streams'][0].get('codec_type') == 'video':
                return True
            return False
        except Exception:
            return False
    
    # 首先根据文件扩展名过滤
    video_files = [f for f in input_path.iterdir() if f.suffix.lower() in supported_formats]
    
    # 如果用户选择了"自动检测所有视频文件"选项
    if config.get('auto_detect_videos', False):
        # 添加没有扩展名或扩展名不在支持列表中但可能是视频的文件
        potential_videos = [f for f in input_path.iterdir() 
                           if f.is_file() and f not in video_files]
        
        # 使用ffprobe检测这些文件是否为视频
        for file in potential_videos:
            if is_valid_video(file):
                video_files.append(file)
                logging.info(f"自动检测到视频文件: {file.name}")
    
    video_files = filter_videos(video_files, config['min_size'], config['max_size'])

    if not video_files:
        error_msg = f"输入文件夹 '{config['input_dir']}' 中没有符合条件的视频文件。"
        logging.error(error_msg)
        raise RuntimeError(error_msg)

    total_files = len(video_files)
    completed_files = 0

    # 创建一个字典来跟踪每个future对应的文件
    future_to_file = {}
    
    def update_progress(file_progress, current_file):
        nonlocal completed_files
        total_progress = (completed_files + file_progress) / total_files
        if progress_callback:
            progress_callback(total_progress, file_progress, current_file.name)

    # 设置线程池大小为CPU核心数-1，确保系统有足够资源响应
    max_workers = max(1, min(total_files, psutil.cpu_count(logical=False) - 1))
    with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
        futures = []
        for video_file in video_files:
            future = executor.submit(
                process_single_video, 
                video_file, 
                config, 
                output_path, 
                lambda p, f=video_file: update_progress(p, f),  # 传递当前文件
                pause_event
            )
            future_to_file[future] = video_file
            futures.append(future)

        for future in concurrent.futures.as_completed(futures):
            if pause_event and pause_event.is_set():
                for f in futures:
                    f.cancel()
                break

            current_file = future_to_file[future]
            try:
                result = future.result()
                if result:
                    update_progress(1.0, current_file)  # 先更新为100%完成
                    completed_files += 1  # 然后再增加完成计数
                else:
                    update_progress(0, current_file)  # 如果跳过则显示0%
            except Exception as e:
                logging.error(f"处理文件 '{current_file.name}' 时发生错误: {e}")
            finally:
                future_to_file.pop(future, None)

    logging.info("所有转码任务完成。")

if __name__ == "__main__":
    set_terminal_encoding()
    setup_logging()
    check_dependencies()
    
    config = load_config()
    args = parse_arguments()
    
    # 从命令行参数中更新配置
    args_dict = vars(args)
    for key, value in args_dict.items():
        # 只有在命令行参数明确提供且不为 None 时才更新配置
        if value is not None and key != 'video_format':
            config[key] = value
    
    # 如果命令行提供了 video_format，则使用命令行的值；否则保持配置文件的值
    if args.video_format is not None:
        config['video_format'] = args.video_format
    
    try:
        validate_arguments(args, config)
    except ValueError as e:
        logging.error(f"参数验证失败: {e}")
        sys.exit(1)
    
    save_config(config)
    transcode_videos_with_detailed_progress(config)
