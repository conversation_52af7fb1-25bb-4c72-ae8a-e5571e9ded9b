@echo off
chcp 65001 > nul
setlocal enabledelayedexpansion

:: 设置标题(使用英文避免乱码)
title Video Batch Converter Launcher

:: 激活虚拟环境并设置Qt插件路径
if exist ".venv\Scripts\activate.bat" (
    call ".venv\Scripts\activate.bat"
    set QT_QPA_PLATFORM_PLUGIN_PATH=.venv\Lib\site-packages\PyQt5\Qt5\plugins
) else (
    echo 未找到虚拟环境，将使用系统Python环境
    set QT_QPA_PLATFORM_PLUGIN_PATH=%PYTHONPATH%\Lib\site-packages\PyQt5\Qt5\plugins
)

:: 检查 Python 是否已安装
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo 未检测到 Python，请安装 Python 3.x 版本
    pause
    exit /b 1
)

:: 检查必要的库是否已安装
python -c "import PyQt5" >nul 2>&1
if %errorlevel% neq 0 (
    echo 未检测到 PyQt5，正在尝试自动安装...
    pip install PyQt5 psutil --no-warn-script-location
    if %errorlevel% neq 0 (
        echo 自动安装 PyQt5 失败，请手动安装：pip install PyQt5 psutil
        pause
        exit /b 1
    )
)

:: 检查是否存在可执行文件
if exist "视频批量转换工具.exe" (
    echo 检测到可执行文件，直接启动...
    start "" "视频批量转换工具.exe"
    exit /b 0
)

:: 运行图形界面
echo 启动图形界面...
echo 当前Python路径: %PYTHONPATH%
pythonw.exe gui.py 2> gui_error.log
if %errorlevel% neq 0 (
    echo 启动图形界面失败，错误日志已保存到gui_error.log
    type gui_error.log
    pause
    exit /b 1
)
