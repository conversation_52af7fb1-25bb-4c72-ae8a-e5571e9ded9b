@echo off
chcp 65001 > nul
setlocal enabledelayedexpansion

:: 检查是否在虚拟环境中
if not exist ".venv\Scripts\activate.bat" (
    echo 错误：请在虚拟环境中运行此脚本
    pause
    exit /b 1
)

:: 激活虚拟环境
call ".venv\Scripts\activate.bat"

:: 安装打包工具
pip install pyinstaller

:: 设置Qt插件路径
set QT_QPA_PLATFORM_PLUGIN_PATH=.venv\Lib\site-packages\PyQt5\Qt5\plugins

@echo off
chcp 65001 > nul

:: 激活虚拟环境
if exist ".venv\Scripts\activate.bat" (
    call ".venv\Scripts\activate.bat"
) else (
    echo 虚拟环境不存在，请先创建虚拟环境
    pause
    exit /b 1
)

:: 执行打包(移除图标参数)
pyinstaller --onefile --windowed ^
    --name "视频批量转换工具" ^
    --add-data ".venv/Lib/site-packages/PyQt5/Qt5/plugins;PyQt5/Qt5/plugins" ^
    --add-data ".venv/Lib/site-packages/PyQt5/Qt5/bin;PyQt5/Qt5/bin" ^
    --add-data "ffmpeg;ffmpeg" ^
    gui.py

pause

if %errorlevel% neq 0 (
    echo 打包失败
    pause
    exit /b 1
)

echo 打包成功，可执行文件在dist文件夹中
pause