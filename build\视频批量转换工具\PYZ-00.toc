('C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\build\\视频批量转换工具\\PYZ-00.pyz',
 [('PyQt5',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\site-packages\\PyQt5\\__init__.py',
   'PYMODULE'),
  ('_compat_pickle',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\_compat_pickle.py',
   'PYMODULE'),
  ('_compression',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\_compression.py',
   'PYMODULE'),
  ('_py_abc',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\_py_abc.py',
   'PYMODULE'),
  ('_pydecimal',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\_pydecimal.py',
   'PYMODULE'),
  ('_pyi_rth_utils',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\site-packages\\PyInstaller\\fake-modules\\_pyi_rth_utils\\__init__.py',
   'PYMODULE'),
  ('_pyi_rth_utils.qt',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\site-packages\\PyInstaller\\fake-modules\\_pyi_rth_utils\\qt.py',
   'PYMODULE'),
  ('_strptime',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\_strptime.py',
   'PYMODULE'),
  ('_threading_local',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\_threading_local.py',
   'PYMODULE'),
  ('argparse',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\argparse.py',
   'PYMODULE'),
  ('ast', 'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\ast.py', 'PYMODULE'),
  ('base64',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\base64.py',
   'PYMODULE'),
  ('bisect',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\bisect.py',
   'PYMODULE'),
  ('bz2', 'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\bz2.py', 'PYMODULE'),
  ('calendar',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\calendar.py',
   'PYMODULE'),
  ('concurrent',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\concurrent\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\concurrent\\futures\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures._base',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\concurrent\\futures\\_base.py',
   'PYMODULE'),
  ('concurrent.futures.process',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\concurrent\\futures\\process.py',
   'PYMODULE'),
  ('concurrent.futures.thread',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\concurrent\\futures\\thread.py',
   'PYMODULE'),
  ('contextlib',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\contextlib.py',
   'PYMODULE'),
  ('contextvars',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\contextvars.py',
   'PYMODULE'),
  ('copy', 'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\copy.py', 'PYMODULE'),
  ('csv', 'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\csv.py', 'PYMODULE'),
  ('ctypes',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\ctypes\\__init__.py',
   'PYMODULE'),
  ('ctypes._endian',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\ctypes\\_endian.py',
   'PYMODULE'),
  ('dataclasses',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\dataclasses.py',
   'PYMODULE'),
  ('datetime',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\datetime.py',
   'PYMODULE'),
  ('decimal',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\decimal.py',
   'PYMODULE'),
  ('dis', 'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\dis.py', 'PYMODULE'),
  ('email',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\email\\__init__.py',
   'PYMODULE'),
  ('email._encoded_words',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('email._header_value_parser',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('email._parseaddr',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\email\\_parseaddr.py',
   'PYMODULE'),
  ('email._policybase',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\email\\_policybase.py',
   'PYMODULE'),
  ('email.base64mime',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\email\\base64mime.py',
   'PYMODULE'),
  ('email.charset',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\email\\charset.py',
   'PYMODULE'),
  ('email.contentmanager',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.encoders',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\email\\encoders.py',
   'PYMODULE'),
  ('email.errors',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\email\\errors.py',
   'PYMODULE'),
  ('email.feedparser',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\email\\feedparser.py',
   'PYMODULE'),
  ('email.generator',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\email\\generator.py',
   'PYMODULE'),
  ('email.header',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\email\\header.py',
   'PYMODULE'),
  ('email.headerregistry',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email.iterators',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\email\\iterators.py',
   'PYMODULE'),
  ('email.message',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\email\\message.py',
   'PYMODULE'),
  ('email.parser',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\email\\parser.py',
   'PYMODULE'),
  ('email.policy',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\email\\policy.py',
   'PYMODULE'),
  ('email.quoprimime',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\email\\quoprimime.py',
   'PYMODULE'),
  ('email.utils',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\email\\utils.py',
   'PYMODULE'),
  ('ffmpeg_utils',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\ffmpeg_utils.py',
   'PYMODULE'),
  ('fnmatch',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\fnmatch.py',
   'PYMODULE'),
  ('fractions',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\fractions.py',
   'PYMODULE'),
  ('ftplib',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\ftplib.py',
   'PYMODULE'),
  ('getopt',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\getopt.py',
   'PYMODULE'),
  ('getpass',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\getpass.py',
   'PYMODULE'),
  ('gettext',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\gettext.py',
   'PYMODULE'),
  ('gzip', 'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\gzip.py', 'PYMODULE'),
  ('hashlib',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\hashlib.py',
   'PYMODULE'),
  ('hmac', 'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\hmac.py', 'PYMODULE'),
  ('http',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\http\\__init__.py',
   'PYMODULE'),
  ('http.client',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\http\\client.py',
   'PYMODULE'),
  ('http.cookiejar',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\http\\cookiejar.py',
   'PYMODULE'),
  ('importlib',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\importlib\\__init__.py',
   'PYMODULE'),
  ('importlib._abc',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\importlib\\_abc.py',
   'PYMODULE'),
  ('importlib._bootstrap',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.abc',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\importlib\\abc.py',
   'PYMODULE'),
  ('importlib.machinery',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('importlib.metadata',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\importlib\\metadata\\__init__.py',
   'PYMODULE'),
  ('importlib.metadata._adapters',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\importlib\\metadata\\_adapters.py',
   'PYMODULE'),
  ('importlib.metadata._collections',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\importlib\\metadata\\_collections.py',
   'PYMODULE'),
  ('importlib.metadata._functools',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\importlib\\metadata\\_functools.py',
   'PYMODULE'),
  ('importlib.metadata._itertools',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\importlib\\metadata\\_itertools.py',
   'PYMODULE'),
  ('importlib.metadata._meta',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\importlib\\metadata\\_meta.py',
   'PYMODULE'),
  ('importlib.metadata._text',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\importlib\\metadata\\_text.py',
   'PYMODULE'),
  ('importlib.readers',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\importlib\\readers.py',
   'PYMODULE'),
  ('importlib.resources',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\importlib\\resources\\__init__.py',
   'PYMODULE'),
  ('importlib.resources._adapters',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\importlib\\resources\\_adapters.py',
   'PYMODULE'),
  ('importlib.resources._common',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\importlib\\resources\\_common.py',
   'PYMODULE'),
  ('importlib.resources._itertools',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\importlib\\resources\\_itertools.py',
   'PYMODULE'),
  ('importlib.resources._legacy',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\importlib\\resources\\_legacy.py',
   'PYMODULE'),
  ('importlib.resources.abc',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\importlib\\resources\\abc.py',
   'PYMODULE'),
  ('importlib.resources.readers',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\importlib\\resources\\readers.py',
   'PYMODULE'),
  ('importlib.util',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\importlib\\util.py',
   'PYMODULE'),
  ('inspect',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\inspect.py',
   'PYMODULE'),
  ('ipaddress',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\ipaddress.py',
   'PYMODULE'),
  ('json',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\json\\__init__.py',
   'PYMODULE'),
  ('json.decoder',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\json\\decoder.py',
   'PYMODULE'),
  ('json.encoder',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\json\\encoder.py',
   'PYMODULE'),
  ('json.scanner',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\json\\scanner.py',
   'PYMODULE'),
  ('logging',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\logging\\__init__.py',
   'PYMODULE'),
  ('lzma', 'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\lzma.py', 'PYMODULE'),
  ('mimetypes',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\mimetypes.py',
   'PYMODULE'),
  ('multiprocessing',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\multiprocessing\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.connection',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\multiprocessing\\connection.py',
   'PYMODULE'),
  ('multiprocessing.context',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\multiprocessing\\context.py',
   'PYMODULE'),
  ('multiprocessing.dummy',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\multiprocessing\\dummy\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.dummy.connection',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\multiprocessing\\dummy\\connection.py',
   'PYMODULE'),
  ('multiprocessing.forkserver',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\multiprocessing\\forkserver.py',
   'PYMODULE'),
  ('multiprocessing.heap',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\multiprocessing\\heap.py',
   'PYMODULE'),
  ('multiprocessing.managers',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\multiprocessing\\managers.py',
   'PYMODULE'),
  ('multiprocessing.pool',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\multiprocessing\\pool.py',
   'PYMODULE'),
  ('multiprocessing.popen_fork',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\multiprocessing\\popen_fork.py',
   'PYMODULE'),
  ('multiprocessing.popen_forkserver',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\multiprocessing\\popen_forkserver.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_posix',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\multiprocessing\\popen_spawn_posix.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_win32',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\multiprocessing\\popen_spawn_win32.py',
   'PYMODULE'),
  ('multiprocessing.process',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\multiprocessing\\process.py',
   'PYMODULE'),
  ('multiprocessing.queues',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\multiprocessing\\queues.py',
   'PYMODULE'),
  ('multiprocessing.reduction',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\multiprocessing\\reduction.py',
   'PYMODULE'),
  ('multiprocessing.resource_sharer',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\multiprocessing\\resource_sharer.py',
   'PYMODULE'),
  ('multiprocessing.resource_tracker',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\multiprocessing\\resource_tracker.py',
   'PYMODULE'),
  ('multiprocessing.shared_memory',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\multiprocessing\\shared_memory.py',
   'PYMODULE'),
  ('multiprocessing.sharedctypes',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\multiprocessing\\sharedctypes.py',
   'PYMODULE'),
  ('multiprocessing.spawn',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\multiprocessing\\spawn.py',
   'PYMODULE'),
  ('multiprocessing.synchronize',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\multiprocessing\\synchronize.py',
   'PYMODULE'),
  ('multiprocessing.util',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\multiprocessing\\util.py',
   'PYMODULE'),
  ('netrc',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\netrc.py',
   'PYMODULE'),
  ('nturl2path',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\nturl2path.py',
   'PYMODULE'),
  ('numbers',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\numbers.py',
   'PYMODULE'),
  ('opcode',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\opcode.py',
   'PYMODULE'),
  ('pathlib',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\pathlib.py',
   'PYMODULE'),
  ('pickle',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\pickle.py',
   'PYMODULE'),
  ('pkgutil',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\pkgutil.py',
   'PYMODULE'),
  ('pprint',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\pprint.py',
   'PYMODULE'),
  ('psutil',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\site-packages\\psutil\\__init__.py',
   'PYMODULE'),
  ('psutil._common',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\site-packages\\psutil\\_common.py',
   'PYMODULE'),
  ('psutil._pswindows',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\site-packages\\psutil\\_pswindows.py',
   'PYMODULE'),
  ('py_compile',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\py_compile.py',
   'PYMODULE'),
  ('queue',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\queue.py',
   'PYMODULE'),
  ('quopri',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\quopri.py',
   'PYMODULE'),
  ('random',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\random.py',
   'PYMODULE'),
  ('runpy',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\runpy.py',
   'PYMODULE'),
  ('script', 'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\script.py', 'PYMODULE'),
  ('secrets',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\secrets.py',
   'PYMODULE'),
  ('selectors',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\selectors.py',
   'PYMODULE'),
  ('shlex',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\shlex.py',
   'PYMODULE'),
  ('shutil',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\shutil.py',
   'PYMODULE'),
  ('signal',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\signal.py',
   'PYMODULE'),
  ('socket',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\socket.py',
   'PYMODULE'),
  ('ssl', 'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\ssl.py', 'PYMODULE'),
  ('statistics',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\statistics.py',
   'PYMODULE'),
  ('string',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\string.py',
   'PYMODULE'),
  ('stringprep',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\stringprep.py',
   'PYMODULE'),
  ('subprocess',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\subprocess.py',
   'PYMODULE'),
  ('tarfile',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\tarfile.py',
   'PYMODULE'),
  ('tempfile',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\tempfile.py',
   'PYMODULE'),
  ('textwrap',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\textwrap.py',
   'PYMODULE'),
  ('threading',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\threading.py',
   'PYMODULE'),
  ('token',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\token.py',
   'PYMODULE'),
  ('tokenize',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\tokenize.py',
   'PYMODULE'),
  ('tracemalloc',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\tracemalloc.py',
   'PYMODULE'),
  ('typing',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\typing.py',
   'PYMODULE'),
  ('urllib',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\urllib\\__init__.py',
   'PYMODULE'),
  ('urllib.error',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\urllib\\error.py',
   'PYMODULE'),
  ('urllib.parse',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\urllib\\parse.py',
   'PYMODULE'),
  ('urllib.request',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\urllib\\request.py',
   'PYMODULE'),
  ('urllib.response',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\urllib\\response.py',
   'PYMODULE'),
  ('video_utils',
   'C:\\Users\\<USER>\\Music\\批量视频转换-GUI\\video_utils.py',
   'PYMODULE'),
  ('xml',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\xml\\__init__.py',
   'PYMODULE'),
  ('xml.parsers',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\xml\\parsers\\__init__.py',
   'PYMODULE'),
  ('xml.parsers.expat',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\xml\\parsers\\expat.py',
   'PYMODULE'),
  ('xml.sax',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\xml\\sax\\__init__.py',
   'PYMODULE'),
  ('xml.sax._exceptions',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\xml\\sax\\_exceptions.py',
   'PYMODULE'),
  ('xml.sax.expatreader',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\xml\\sax\\expatreader.py',
   'PYMODULE'),
  ('xml.sax.handler',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\xml\\sax\\handler.py',
   'PYMODULE'),
  ('xml.sax.saxutils',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\xml\\sax\\saxutils.py',
   'PYMODULE'),
  ('xml.sax.xmlreader',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\xml\\sax\\xmlreader.py',
   'PYMODULE'),
  ('xmlrpc',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\xmlrpc\\__init__.py',
   'PYMODULE'),
  ('xmlrpc.client',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\xmlrpc\\client.py',
   'PYMODULE'),
  ('zipfile',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\zipfile.py',
   'PYMODULE'),
  ('zipimport',
   'C:\\Users\\<USER>\\miniconda3\\envs\\zzz\\Lib\\zipimport.py',
   'PYMODULE')])
