# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Commands

- **Build the project (virtual environment only)**:
  ```bash
  build_exe.bat
  ```

- **Install dependencies**:
  ```bash
  pip install -r requirements.txt
  ```

- **Run tests (if available)**:
  ```bash
  pytest tests/
  ```

## Architecture

- **Main Scripts**:
  - `gui.py`: Main GUI application for video conversion.
  - `video_utils.py`: Core video conversion logic.
  - `ffmpeg_utils.py`: Utilities for interacting with FFmpeg.

- **Directories**:
  - `input/`: Place input video files here.
  - `output/`: Converted videos are saved here.
  - `ffmpeg/`: Contains FFmpeg binaries.

- **Configuration**:
  - `config.json`: Configuration file for the tool.

- **Build Output**:
  - `dist/`: Contains the built executable and required files.

## Notes

- The tool is designed for Windows and uses FFmpeg for video processing.
- Ensure FFmpeg binaries are present in the `ffmpeg/` directory before building or running.